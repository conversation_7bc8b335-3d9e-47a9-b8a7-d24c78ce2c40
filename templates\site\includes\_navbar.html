{% load static %}

<nav class="navbar navbar-expand-lg navbar-modern sticky-top" id="mainNavbar">
    <div class="container-fluid px-4">
        <!-- Brand -->
        <a class="navbar-brand d-flex align-items-center" href="{% url 'mainsite:home' %}">
            <div class="brand-icon me-2">
                <i class="fas fa-broadcast-tower"></i>
            </div>
            <div class="brand-text">
                <span class="brand-main">NBC</span>
                <span class="brand-sub">NEWS</span>
            </div>
        </a>

        <!-- Mobile Toggle -->
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
            <span class="navbar-toggler-icon"></span>
        </button>

        <!-- Navigation Links -->
        <div class="collapse navbar-collapse" id="navbarNav">
            <ul class="navbar-nav me-auto">
                <li class="nav-item">
                    <a class="nav-link {% if request.resolver_match.url_name == 'home' %}active{% endif %}" href="{% url 'mainsite:home' %}">
                        <i class="fas fa-home me-1"></i>Home
                    </a>
                </li>

                <!-- Dynamic Categories -->
                {% for category in categories %}
                <li class="nav-item">
                    <a class="nav-link {% if request.resolver_match.url_name == 'category' and category.slug in request.path %}active{% endif %}" href="{% url 'mainsite:category' category.slug %}">
                        {{ category.name }}
                    </a>
                </li>
                {% endfor %}
                
                <li class="nav-item">
                    <a class="nav-link" href="{% url 'mainsite:blog' %}">
                        <i class="fas fa-blog me-1"></i>All News
                    </a>
                </li>
            </ul>

            <!-- Right Side Navigation -->
            <ul class="navbar-nav">
                <!-- Search -->
                <li class="nav-item dropdown">
                    <a class="nav-link" href="#" data-bs-toggle="dropdown">
                        <i class="fas fa-search"></i>
                    </a>
                    <div class="dropdown-menu dropdown-menu-end p-3" style="width: 300px;">
                        <form class="d-flex">
                            <input class="form-control me-2" type="search" placeholder="Search news..." aria-label="Search">
                            <button class="btn btn-outline-primary" type="submit">
                                <i class="fas fa-search"></i>
                            </button>
                        </form>
                    </div>
                </li>

                <!-- Weather Widget -->
                <li class="nav-item">
                    <a class="nav-link" href="#" id="weather-widget">
                        <i class="fas fa-cloud-sun me-1"></i>
                        <span class="d-none d-md-inline">Weather</span>
                    </a>
                </li>

                <!-- Dark Mode Toggle -->
                <li class="nav-item">
                    <a class="nav-link" href="#" onclick="toggleDarkMode(); return false;" id="darkModeToggle">
                        <i class="fas fa-moon me-1"></i>
                        <span class="d-none d-lg-inline">Dark</span>
                    </a>
                </li>

                <!-- Live TV -->
                <li class="nav-item">
                    <a class="nav-link text-danger" href="#" data-bs-toggle="modal" data-bs-target="#liveModal">
                        <i class="fas fa-play-circle me-1"></i>
                        <span class="d-none d-md-inline">LIVE</span>
                    </a>
                </li>

                <!-- User Account -->
                {% if user.is_authenticated %}
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" data-bs-toggle="dropdown">
                        <i class="fas fa-user-circle me-1"></i>{{ user.username }}
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="#"><i class="fas fa-user me-2"></i>Profile</a></li>
                        <li><a class="dropdown-item" href="#"><i class="fas fa-bookmark me-2"></i>Saved Articles</a></li>
                        <li><a class="dropdown-item" href="#"><i class="fas fa-cog me-2"></i>Settings</a></li>
                        <li><hr class="dropdown-divider"></li>
                        {% if user.is_staff %}
                        <li><a class="dropdown-item" href="{% url 'custom_admin:dashboard' %}"><i class="fas fa-tachometer-alt me-2"></i>Admin Panel</a></li>
                        <li><hr class="dropdown-divider"></li>
                        {% endif %}
                        <li><a class="dropdown-item" href="{% url 'account:logout' %}"><i class="fas fa-sign-out-alt me-2"></i>Logout</a></li>
                    </ul>
                </li>
                {% else %}
                <li class="nav-item">
                    <a class="nav-link" href="{% url 'account:login' %}">
                        <i class="fas fa-sign-in-alt me-1"></i>Login
                    </a>
                </li>
                <li class="nav-item">
                    <a class="btn btn-modern" href="{% url 'account:register' %}">
                        <i class="fas fa-user-plus me-1"></i>Sign Up
                    </a>
                </li>
                {% endif %}
            </ul>
        </div>
    </div>
</nav>

<script>
// Navbar scroll effect
window.addEventListener('scroll', function() {
    const navbar = document.getElementById('mainNavbar');
    if (window.scrollY > 50) {
        navbar.classList.add('scrolled');
    } else {
        navbar.classList.remove('scrolled');
    }
});
</script>

<!-- Live TV Modal -->
<div class="modal fade" id="liveModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-play-circle text-danger me-2"></i>NBC News Live
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="ratio ratio-16x9">
                    <div class="d-flex align-items-center justify-content-center bg-dark text-white">
                        <div class="text-center">
                            <i class="fas fa-play-circle fa-3x mb-3"></i>
                            <h4>Live Stream</h4>
                            <p>NBC News Live Stream would be embedded here</p>
                            <button class="btn btn-danger">
                                <i class="fas fa-play me-2"></i>Watch Live
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Weather Widget (placeholder)
document.addEventListener('DOMContentLoaded', function() {
    const weatherWidget = document.getElementById('weather-widget');
    if (weatherWidget) {
        // Simulate weather data
        setTimeout(() => {
            weatherWidget.innerHTML = '<i class="fas fa-sun me-1"></i><span class="d-none d-md-inline">72°F</span>';
        }, 1000);
    }
});
</script>
