{% if is_paginated %}
<div class="col-lg-12 col-md-12">
    <div class="pagination-area">
        <nav aria-label="Page navigation">
            <ul class="pagination justify-content-center">
                {% if page_obj.has_previous %}
                <li class="page-item">
                    <a class="page-link" href="?page={{ page_obj.previous_page_number }}" aria-label="Previous">
                        <i class="icofont-stylish-left"></i>
                    </a>
                </li>
                {% else %}
                    <li class="page-item disabled">
                        <a class="page-link">&laquo;</a>
                    </li>
                {% endif %}
                {% for i in page_obj.paginator.page_range %}
                    {% if page_obj.number == i %}
                        <li class="page-item active">
                            <a class="page-link">{{ i }}</a>
                        </li>
                    {% else %}
                        <li class="page-item">
                            <a href="?page={{ i }}" class="page-link">{{ i }}</a>
                        </li>
                    {% endif %}
                {% endfor %}
                {% if page_obj.has_next %}
                    <li class="page-item">
                        <a href="?page={{ page_obj.next_page_number }}" class="page-link">&raquo;</a>
                    </li>
                {% else %}
                    <li class="page-item disabled">
                        <a class="page-link">&raquo;</a>
                    </li>
                {% endif %}
            </ul>
        </nav>
    </div>
</div>
{% endif %}

