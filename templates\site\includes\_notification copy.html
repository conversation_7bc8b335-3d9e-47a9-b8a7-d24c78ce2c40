{% load static %}
{% if messages %}
<link href="{% static 'assets/css/toastr.css' %}" rel="stylesheet">
<script src="{% static 'assets/js/toastr.js' %}"></script>

{% for message in messages %}

<script>
    (function($) {

    var type = "{{ message.tags }}";
      console.log(type);
      switch(type){
          case 'info':
                toastr.info("{{ message }}");
                break;
          case 'warning':
                toastr.warning("{{ message }}");
                break;
          case 'success':
                toastr.success("{{ message }}");
                break;
          case 'error':
                toastr.error("{{ message }}");
                break;
      }
   
})(jQuery);
</script>


{% endfor %}
{% endif %}