{% extends 'site/base.html' %}
{% load static %}
{% load ad_tags %}

{% block title %}Breaking News, Latest Updates{% endblock %}

{% block content %}
<!-- Breaking/Featured News Section -->
<section class="breaking-news-section mb-3">
    <div class="container px-4">
        <div class="row align-items-center">
            <div class="col-lg-8 col-md-12 mb-2 mb-lg-0">
                {% if hot_news %}
                <article class="entity_wrapper d-flex flex-column flex-md-row gap-3 align-items-md-center p-0 bg-white shadow-sm">
                    <div class="entity_thumb flex-shrink-0" style="max-width: 340px;">
                        <img class="img-responsive" style="width: 100%; max-height: 220px; min-height: 120px; object-fit: cover;" src="{% if hot_news.thumbnail_url %}{{ hot_news.thumbnail_url }}{% elif hot_news.thumbnail %}{{ hot_news.thumbnail.url }}{% else %}{% static 'assets/img/feature-top.jpg' %}{% endif %}" alt="{{ hot_news.title }}" onerror="this.src='{% static 'assets/img/feature-top.jpg' %}'" loading="lazy">
                    </div>
                    <div class="ps-md-4 pt-2 pt-md-0 flex-grow-1">
                        <header class="entity_title mb-1">
                            <a href="{{ hot_news.get_absolute_url }}" target="_blank"><h1>{{ hot_news.title }}</h1></a>
                        </header>
                        <div class="entity_meta mb-1">
                            <span>{{ hot_news.timestamp|date:"j M Y" }}</span>, by: <span>{{ hot_news.author|title }}</span>
                        </div>
                        <div class="entity_content mb-1">
                            {{ hot_news.description|truncatechars:180 }}
                        </div>
                    </div>
                </article>
                {% endif %}
            </div>
            <div class="col-lg-4 col-md-12">
                <!-- Top Stories Grid -->
                <section class="top-stories-section">
                    <h2 class="section-title mb-2">Top Stories</h2>
                    <div class="row g-2">
                        {% for news in top_stories|slice:":4" %}
                        <div class="col-6">
                            <article class="entity_wrapper p-2 bg-white shadow-sm h-100 d-flex flex-column gap-1">
                                <div class="entity_thumb mb-1">
                                    <img class="img-responsive" style="width: 100%; max-height: 100px; object-fit: cover;" src="{% if news.thumbnail_url %}{{ news.thumbnail_url }}{% elif news.thumbnail %}{{ news.thumbnail.url }}{% else %}{% static 'assets/img/category_img2.jpg' %}{% endif %}" alt="{{ news.title }}" onerror="this.src='{% static 'assets/img/category_img2.jpg' %}'" loading="lazy">
                                </div>
                                <header class="entity_title mb-0">
                                    <a href="{{ news.get_absolute_url }}"><h4>{{ news.title|truncatechars:55 }}</h4></a>
                                </header>
                                <div class="entity_meta small">
                                    <span>{{ news.timestamp|timesince }} ago</span>
                                </div>
                            </article>
                        </div>
                        {% endfor %}
                    </div>
                </section>
            </div>
        </div>
    </div>
</section>

<!-- More Headlines Section -->
<section class="more-headlines-section mb-3">
    <div class="container px-4">
        <header class="header_purple mb-2">
            <h2 class="section-title">More Headlines</h2>
        </header>
        <div class="row g-3">
            {% for news in latest_news|slice:":4" %}
            <div class="col-md-3 col-12">
                <article class="entity_wrapper p-2 bg-white shadow-sm h-100 d-flex flex-column gap-1">
                    <div class="entity_thumb mb-1">
                        <img class="img-responsive" style="width: 100%; max-height: 120px; object-fit: cover;" src="{% if news.thumbnail_url %}{{ news.thumbnail_url }}{% elif news.thumbnail %}{{ news.thumbnail.url }}{% else %}{% static 'assets/img/category_img1.jpg' %}{% endif %}" alt="{{ news.title }}" onerror="this.src='{% static 'assets/img/category_img1.jpg' %}'" loading="lazy">
                    </div>
                    <header class="entity_title mb-0">
                        <a href="{{ news.get_absolute_url }}"><h4>{{ news.title|truncatechars:70 }}</h4></a>
                    </header>
                    <div class="entity_meta small">
                        <span>{{ news.timestamp|date:"j M Y" }}</span>
                    </div>
                </article>
            </div>
            {% endfor %}
        </div>
    </div>
</section>

<!-- Trending & Live/Latest News Section -->
<section class="trending-live-section mb-3">
    <div class="container px-4">
        <div class="row g-3">
            <div class="col-lg-4 col-md-6">
                <section class="trending-stories-mini">
                    <h3 class="section-title mb-2"><i class="fas fa-fire"></i> Trending Now</h3>
                    {% for news in trending_news|slice:":4" %}
                    <article class="entity_wrapper p-2 bg-white shadow-sm mb-2 d-flex flex-row gap-2 align-items-center">
                        <div class="entity_thumb flex-shrink-0" style="width: 60px;">
                            <img class="img-responsive" style="width: 100%; max-height: 60px; object-fit: cover;" src="{% if news.thumbnail_url %}{{ news.thumbnail_url }}{% elif news.thumbnail %}{{ news.thumbnail.url }}{% else %}{% static 'assets/img/category_img2.jpg' %}{% endif %}" alt="{{ news.title }}" onerror="this.src='{% static 'assets/img/category_img2.jpg' %}'" loading="lazy">
                        </div>
                        <div class="flex-grow-1">
                            <header class="entity_title mb-0">
                                <a href="{{ news.get_absolute_url }}"><h6 class="mb-0">{{ news.title|truncatechars:50 }}</h6></a>
                            </header>
                            <div class="entity_meta small">
                                <span>{{ news.views_count }} views</span>
                            </div>
                        </div>
                    </article>
                    {% endfor %}
                </section>
            </div>
            <div class="col-lg-4 col-md-6">
                <section class="live-section">
                    <h3 class="section-title mb-2"><i class="fas fa-broadcast-tower"></i> Live / Editor's Choice</h3>
                    {% if editor_choice %}
                    <article class="entity_wrapper p-2 bg-white shadow-sm d-flex flex-row gap-2 align-items-center">
                        <div class="entity_thumb flex-shrink-0" style="width: 60px;">
                            <img class="img-responsive" style="width: 100%; max-height: 60px; object-fit: cover;" src="{% if editor_choice.thumbnail_url %}{{ editor_choice.thumbnail_url }}{% elif editor_choice.thumbnail %}{{ editor_choice.thumbnail.url }}{% else %}{% static 'assets/img/editor.jpg' %}{% endif %}" alt="{{ editor_choice.title }}" onerror="this.src='{% static 'assets/img/editor.jpg' %}'" loading="lazy">
                        </div>
                        <div class="flex-grow-1">
                            <header class="entity_title mb-0">
                                <a href="{{ editor_choice.get_absolute_url }}"><h6 class="mb-0">{{ editor_choice.title|truncatechars:80 }}</h6></a>
                            </header>
                        </div>
                    </article>
                    {% endif %}
                </section>
            </div>
            <div class="col-lg-4 col-md-12">
                <section class="latest-news-section">
                    <h3 class="section-title mb-2"><i class="fas fa-clock"></i> Latest Updates</h3>
                    {% for news in latest_news|slice:":4" %}
                    <article class="entity_wrapper p-2 bg-white shadow-sm mb-2 d-flex flex-row gap-2 align-items-center">
                        <div class="entity_thumb flex-shrink-0" style="width: 60px;">
                            <img class="img-responsive" style="width: 100%; max-height: 60px; object-fit: cover;" src="{% if news.thumbnail_url %}{{ news.thumbnail_url }}{% elif news.thumbnail %}{{ news.thumbnail.url }}{% else %}{% static 'assets/img/category_img1.jpg' %}{% endif %}" alt="{{ news.title }}" onerror="this.src='{% static 'assets/img/category_img1.jpg' %}'" loading="lazy">
                        </div>
                        <div class="flex-grow-1">
                            <header class="entity_title mb-0">
                                <a href="{{ news.get_absolute_url }}"><h6 class="mb-0">{{ news.title|truncatechars:70 }}</h6></a>
                            </header>
                            <div class="entity_meta small">
                                <span>{{ news.timestamp|date:"j M Y" }}</span>
                            </div>
                        </div>
                    </article>
                    {% endfor %}
                </section>
            </div>
        </div>
    </div>
</section>

<!-- Horizontal Banner Advertisement -->
{% has_active_ads "banner" "970x250" as has_banner_ads %}
{% if has_banner_ads %}
<section class="horizontal-banner-ad py-4">
    <div class="container">
        <div class="horizontal-ad-banner large">
            {% display_ad "banner" "970x250" "billboard-ad" %}
        </div>
    </div>
</section>
{% endif %}

<!-- Category News Sections -->
<section class="category-news-sections py-5">
    <div class="container">
        <!-- Politics Section -->
        {% if post_catalog_one %}
        <div class="category-section mb-5">
            <div class="row">
                <div class="col-md-8">
                    {% for post in post_catalog_one %}
                    {% if forloop.first %}
                    <div class="category-header mb-4">
                        <h2 class="category-title">
                            <a href="{{ post.category.get_absolute_url }}" class="text-decoration-none">
                                {{ post.category.name }}
                            </a>
                        </h2>
                        <div class="category-line"></div>
                    </div>
                    {% endif %}

                    <div class="category-main-story mb-4">
                        <div class="row">
                            <div class="col-md-5">
                                <div class="category-image">
                                    <a href="{{ post.get_absolute_url }}">
                                        <img src="{% if post.thumbnail_url %}{{ post.thumbnail_url }}{% elif post.thumbnail %}{{ post.thumbnail.url }}{% else %}{% static 'assets/img/category_img3.jpg' %}{% endif %}"
                                             alt="{{ post.title }}" class="img-fluid rounded"
                                             onerror="this.src='{% static 'assets/img/category_img3.jpg' %}'"
                                             loading="lazy">
                                    </a>
                                </div>
                            </div>
                            <div class="col-md-7">
                                <div class="category-content">
                                    {% for tag in post.tags.all %}
                                    <span class="tag-badge">{{ tag }}</span>
                                    {% endfor %}

                                    <h3 class="category-story-title">
                                        <a href="{{ post.get_absolute_url }}">{{ post.title }}</a>
                                    </h3>

                                    <div class="story-meta mb-2">
                                        <span class="meta-date">{{ post.timestamp|date:"M j, Y" }}</span>
                                        <span class="meta-author">by {{ post.author|title }}</span>
                                    </div>

                                    <p class="story-excerpt">{{ post.description|truncatechars:150 }}</p>

                                    <div class="story-engagement">
                                        <span class="engagement-item">
                                            <i class="fas fa-share-alt me-1"></i>424 Shares
                                        </span>
                                        <span class="engagement-item">
                                            <i class="fas fa-comments me-1"></i>{{ post.get_comment_count }} Comments
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    {% if forloop.last %}
                    <div class="category-more">
                        <a href="{{ post.category.get_absolute_url }}" class="more-link">
                            More {{ post.category.name }} News <i class="fas fa-arrow-right ms-1"></i>
                        </a>
                    </div>
                    {% endif %}
                    {% endfor %}
                </div>

                <!-- Category Sidebar -->
                <div class="col-md-4">
                    <div class="category-sidebar">
                        <!-- Related Stories -->
                        <div class="related-stories">
                            <h4 class="sidebar-title">Related Stories</h4>
                            {% for news in latest_news|slice:":3" %}
                            <div class="related-story-item">
                                <h6 class="related-story-title">
                                    <a href="{{ news.get_absolute_url }}">{{ news.title|truncatechars:60 }}</a>
                                </h6>
                                <small class="text-muted">{{ news.timestamp|timesince }} ago</small>
                            </div>
                            {% endfor %}
                        </div>

                        <!-- Sidebar Ad -->
                        {% if has_sidebar_ads %}
                        <div class="sidebar-ad mt-4">
                            {% display_ad "sidebar" "300x250" "category-sidebar-ad" %}
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
        {% endif %}
    </div>
</section>

<!-- Technology Section -->
{% if post_catalog_two %}
<section class="technology-section py-5 bg-light">
    <div class="container">
        <div class="row">
            <div class="col-md-8">
                {% for post in post_catalog_two %}
                {% if forloop.first %}
                <div class="category-header mb-4">
                    <h2 class="category-title tech">
                        <a href="{{ post.category.get_absolute_url }}" class="text-decoration-none">
                            {{ post.category.name }}
                        </a>
                    </h2>
                    <div class="category-line tech"></div>
                </div>
                {% endif %}

                <div class="tech-story-grid">
                    <div class="row">
                        <div class="col-md-6 mb-4">
                            <div class="tech-story-card">
                                <div class="tech-image">
                                    <a href="{{ post.get_absolute_url }}">
                                        <img src="{% if post.thumbnail_url %}{{ post.thumbnail_url }}{% elif post.thumbnail %}{{ post.thumbnail.url }}{% else %}{% static 'assets/img/tech_photo1.jpg' %}{% endif %}"
                                             alt="{{ post.title }}" class="img-fluid rounded"
                                             onerror="this.src='{% static 'assets/img/tech_photo1.jpg' %}'"
                                             loading="lazy">
                                    </a>
                                </div>

                                <div class="tech-content mt-3">
                                    {% for tag in post.tags.all %}
                                    <span class="tag-badge tech">{{ tag }}</span>
                                    {% endfor %}

                                    <h4 class="tech-story-title">
                                        <a href="{{ post.get_absolute_url }}">{{ post.title }}</a>
                                    </h4>

                                    <div class="story-meta mb-2">
                                        <span class="meta-date">{{ post.timestamp|date:"M j, Y" }}</span>
                                        <span class="meta-author">by {{ post.author|title }}</span>
                                    </div>

                                    <p class="story-excerpt">{{ post.description|truncatechars:120 }}</p>

                                    <div class="story-engagement">
                                        <span class="engagement-item">
                                            <i class="fas fa-share-alt me-1"></i>{{ post.get_comment_count }} Comments
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                {% if forloop.last %}
                <div class="category-more">
                    <a href="{{ post.category.get_absolute_url }}" class="more-link tech">
                        More {{ post.category.name }} News <i class="fas fa-arrow-right ms-1"></i>
                    </a>
                </div>
                {% endif %}
                {% endfor %}
            </div>

            <!-- Tech Sidebar -->
            <div class="col-md-4">
                <div class="tech-sidebar">
                    <!-- Trending Tech -->
                    <div class="trending-tech">
                        <h4 class="sidebar-title">Trending in Tech</h4>
                        {% for news in latest_news|slice:"2:5" %}
                        <div class="trending-tech-item">
                            <h6 class="trending-tech-title">
                                <a href="{{ news.get_absolute_url }}">{{ news.title|truncatechars:55 }}</a>
                            </h6>
                            <small class="text-muted">{{ news.timestamp|timesince }} ago</small>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
{% endif %}
<!-- Enhanced News Display Section -->
<section class="enhanced-news-display py-5 bg-light">
    <div class="container">
        <!-- News Statistics Bar -->
        <div class="news-stats-bar mb-4">
            <div class="row text-center">
                <div class="col-md-3">
                    <div class="stat-item">
                        <div class="stat-number">{{ total_news_count }}</div>
                        <div class="stat-label">Total Articles</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-item">
                        <div class="stat-number">{{ total_categories }}</div>
                        <div class="stat-label">Categories</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-item">
                        <div class="stat-number">{{ todays_news|length }}</div>
                        <div class="stat-label">Today's News</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-item">
                        <div class="stat-number">{{ api_news|length }}</div>
                        <div class="stat-label">Live Updates</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Today's News Section -->
        {% if todays_news %}
        <div class="todays-news-section mb-5">
            <h3 class="section-title">
                <i class="fas fa-calendar-day"></i>Today's Headlines
                <span class="news-count-badge">{{ todays_news|length }}</span>
            </h3>
            <div class="row">
                {% for news in todays_news|slice:":6" %}
                <div class="col-lg-4 col-md-6 mb-3">
                    <div class="today-news-card">
                        <div class="news-image">
                            <img src="{% if news.thumbnail_url %}{{ news.thumbnail_url }}{% elif news.thumbnail %}{{ news.thumbnail.url }}{% else %}{% static 'assets/img/category_img1.jpg' %}{% endif %}"
                                 alt="{{ news.title }}"
                                 onerror="this.src='{% static 'assets/img/category_img1.jpg' %}'"
                                 loading="lazy">
                            <div class="news-category-badge">{{ news.category.name }}</div>
                            {% if news.is_breaking %}
                            <div class="breaking-badge">BREAKING</div>
                            {% endif %}
                        </div>
                        <div class="news-content">
                            <h6 class="news-title">
                                <a href="{{ news.get_absolute_url }}">{{ news.title|truncatechars:80 }}</a>
                            </h6>
                            <p class="news-excerpt">{{ news.description|truncatechars:100 }}</p>
                            <div class="news-meta">
                                <span class="news-time">
                                    <i class="fas fa-clock"></i>{{ news.timestamp|timesince }} ago
                                </span>
                                <span class="news-author">
                                    <i class="fas fa-user"></i>{{ news.author.user.get_full_name|default:news.author.user.username }}
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
        {% endif %}

        <!-- API News Section -->
        {% if api_news %}
        <div class="api-news-section mb-5">
            <h3 class="section-title">
                <i class="fas fa-rss"></i>Live News Updates
                <span class="live-badge">LIVE</span>
            </h3>
            <div class="row">
                {% for news in api_news|slice:":8" %}
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="api-news-card">
                        <div class="news-image">
                            <img src="{% if news.thumbnail_url %}{{ news.thumbnail_url }}{% elif news.thumbnail %}{{ news.thumbnail.url }}{% else %}{% static 'assets/img/category_img2.jpg' %}{% endif %}"
                                 alt="{{ news.title }}"
                                 onerror="this.src='{% static 'assets/img/category_img2.jpg' %}'"
                                 loading="lazy">
                            <div class="api-source-badge">NewsData.io</div>
                        </div>
                        <div class="news-content">
                            <h6 class="news-title">
                                <a href="{{ news.get_absolute_url }}">{{ news.title|truncatechars:60 }}</a>
                            </h6>
                            <div class="news-meta">
                                <span class="news-category">{{ news.category.name }}</span>
                                <span class="news-time">{{ news.timestamp|timesince }} ago</span>
                            </div>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
        {% endif %}

        <!-- Dynamic Category Grid -->
        {% if latest_by_category %}
        <div class="dynamic-categories-section">
            <h3 class="section-title">
                <i class="fas fa-th-large"></i>News by Category
            </h3>
            <div class="row">
                {% for category_name, category_data in latest_by_category.items %}
                <div class="col-lg-6 col-xl-4 mb-4">
                    <div class="category-card">
                        <div class="category-header">
                            <h5 class="category-title">
                                <i class="fas fa-{% if category_name == 'Politics' %}landmark{% elif category_name == 'Technology' %}microchip{% elif category_name == 'Sports' %}futbol{% elif category_name == 'Business' %}chart-line{% elif category_name == 'Health' %}heartbeat{% elif category_name == 'World' %}globe{% else %}newspaper{% endif %}"></i>
                                {{ category_name }}
                                <span class="category-count">({{ category_data.count }})</span>
                            </h5>
                            <a href="{% url 'newspaper:category' category_data.category.slug %}" class="view-all-btn">
                                View All
                            </a>
                        </div>

                        <div class="category-news-list">
                            {% for news in category_data.news|slice:":3" %}
                            <div class="category-news-item">
                                <div class="news-thumbnail">
                                    <img src="{% if news.thumbnail_url %}{{ news.thumbnail_url }}{% elif news.thumbnail %}{{ news.thumbnail.url }}{% else %}{% static 'assets/img/category_img3.jpg' %}{% endif %}"
                                         alt="{{ news.title }}"
                                         onerror="this.src='{% static 'assets/img/category_img3.jpg' %}'"
                                         loading="lazy">
                                </div>
                                <div class="news-info">
                                    <h6 class="news-title">
                                        <a href="{{ news.get_absolute_url }}">{{ news.title|truncatechars:50 }}</a>
                                    </h6>
                                    <div class="news-meta">
                                        <span class="news-time">{{ news.timestamp|timesince }} ago</span>
                                        {% if news.is_featured %}
                                        <span class="featured-tag">Featured</span>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
        {% endif %}
    </div>
</section>

<!-- Bottom Horizontal Advertisement -->
{% has_active_ads "footer" "728x90" as has_footer_ads %}
{% if has_footer_ads %}
<section class="bottom-banner-ad py-4">
    <div class="container">
        <div class="horizontal-ad-banner">
            {% display_ad "footer" "728x90" "footer-ad" %}
        </div>
    </div>
</section>
{% endif %}

{% endblock %}
