{% extends 'site/base.html' %}
{% load static %}
{% load ad_tags %}

{% block title %}Breaking News, Latest Updates{% endblock %}

{% block content %}
<!-- Top Banner Ad -->
{% has_active_ads "header" "728x90" as has_header_ads %}
{% if has_header_ads %}
<section class="top-banner-ad py-2">
    <div class="container">
        <div class="horizontal-ad-banner">
            {% display_ad "header" "728x90" "header-ad" %}
        </div>
    </div>
</section>
{% endif %}

<!-- NBC News Style Main Layout -->
<section class="nbc-main-layout py-4">
    <div class="container-fluid">
        <div class="row g-4">
            <!-- Left Column - Main Featured Story -->
            <div class="col-lg-6 col-md-12">
                {% if hot_news %}
                <div class="main-story-card">
                    <div class="story-image-container">
                        <img src="{% if hot_news.thumbnail_url %}{{ hot_news.thumbnail_url }}{% elif hot_news.thumbnail %}{{ hot_news.thumbnail.url }}{% else %}{% static 'assets/img/feature-top.jpg' %}{% endif %}"
                             alt="{{ hot_news.title }}" class="main-story-image"
                             onerror="this.src='{% static 'assets/img/feature-top.jpg' %}'"
                             loading="lazy">
                        {% if hot_news.is_breaking %}
                        <div class="breaking-overlay">
                            <span class="breaking-badge">
                                <i class="fas fa-play me-1"></i>BREAKING
                            </span>
                        </div>
                        {% endif %}
                    </div>
                    <div class="story-content">
                        <h1 class="main-story-title">
                            <a href="{{ hot_news.get_absolute_url }}" class="text-decoration-none">
                                {{ hot_news.title }}
                            </a>
                        </h1>
                        <p class="main-story-excerpt">{{ hot_news.description|truncatechars:180 }}</p>
                        <div class="story-meta">
                            <span class="meta-item">
                                <i class="fas fa-clock me-1"></i>{{ hot_news.timestamp|timesince }} ago
                            </span>
                            <span class="meta-item">
                                <i class="fas fa-eye me-1"></i>{{ hot_news.views_count }} views
                            </span>
                        </div>
                    </div>
                </div>
                {% endif %}

                <!-- Secondary Stories -->
                <div class="secondary-stories mt-4">
                    <h3 class="section-title">More Headlines</h3>
                    <div class="row">
                        {% for news in latest_news|slice:":4" %}
                        <div class="col-md-6 mb-3">
                            <div class="secondary-story-card">
                                <img src="{% if news.thumbnail_url %}{{ news.thumbnail_url }}{% elif news.thumbnail %}{{ news.thumbnail.url }}{% else %}{% static 'assets/img/category_img1.jpg' %}{% endif %}"
                                     alt="{{ news.title }}" class="secondary-story-image"
                                     onerror="this.src='{% static 'assets/img/category_img1.jpg' %}'"
                                     loading="lazy">
                                <div class="secondary-story-content">
                                    <h5 class="secondary-story-title">
                                        <a href="{{ news.get_absolute_url }}">{{ news.title|truncatechars:70 }}</a>
                                    </h5>
                                    <div class="secondary-story-meta">
                                        <span>{{ news.timestamp|timesince }} ago</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>

            <!-- Middle Column - Top Stories with Priority -->
            <div class="col-lg-3 col-md-6">
                <div class="sub-news-section">
                    <h3 class="section-title">
                        <i class="fas fa-star"></i>Top Stories
                    </h3>

                    <!-- High Priority Stories -->
                    {% for news in top_stories|slice:":3" %}
                    <div class="sub-news-item priority-story">
                        <div class="story-priority-indicator">
                            <span class="priority-dot"></span>
                        </div>
                        <div class="sub-news-image">
                            <img src="{% if news.thumbnail_url %}{{ news.thumbnail_url }}{% elif news.thumbnail %}{{ news.thumbnail.url }}{% else %}{% static 'assets/img/category_img2.jpg' %}{% endif %}"
                                 alt="{{ news.title }}"
                                 onerror="this.src='{% static 'assets/img/category_img2.jpg' %}'"
                                 loading="lazy">
                            {% if news.is_featured %}
                            <div class="featured-overlay">
                                <span class="featured-badge">
                                    <i class="fas fa-medal"></i>FEATURED
                                </span>
                            </div>
                            {% endif %}
                        </div>
                        <div class="sub-news-content">
                            <div class="story-category">{{ news.category.name }}</div>
                            <h6 class="sub-news-title">
                                <a href="{{ news.get_absolute_url }}">{{ news.title|truncatechars:55 }}</a>
                            </h6>
                            <div class="sub-news-meta">
                                <span class="meta-time">{{ news.timestamp|timesince }} ago</span>
                                <span class="meta-views">
                                    <i class="fas fa-eye"></i>{{ news.views_count }}
                                </span>
                            </div>
                        </div>
                    </div>
                    {% endfor %}

                    <!-- Trending Stories -->
                    <div class="trending-stories-mini mt-3">
                        <h6 class="mini-section-title">
                            <i class="fas fa-fire"></i>Trending Now
                        </h6>
                        {% for news in trending_news|slice:":3" %}
                        <div class="mini-story-item">
                            <div class="mini-story-rank">{{ forloop.counter }}</div>
                            <div class="mini-story-content">
                                <a href="{{ news.get_absolute_url }}" class="mini-story-title">
                                    {{ news.title|truncatechars:50 }}
                                </a>
                                <div class="mini-story-meta">
                                    <span>{{ news.views_count }} views</span>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>

                <!-- Middle Column Ad -->
                {% has_active_ads "sidebar" "300x250" as has_sidebar_ads %}
                {% if has_sidebar_ads %}
                <div class="middle-ad-section mt-4">
                    {% display_ad "sidebar" "300x250" "middle-ad" %}
                </div>
                {% endif %}
            </div>

            <!-- Right Column - Live Events & Latest News -->
            <div class="col-lg-3 col-md-6">
                <!-- Live Section -->
                <div class="live-section">
                    <div class="live-header">
                        <span class="live-indicator">
                            <span class="live-dot"></span>
                            Live
                        </span>
                        <span class="live-source">/ Dateline NBC</span>
                    </div>
                    {% if editor_choice %}
                    <div class="live-content">
                        <img src="{% if editor_choice.thumbnail_url %}{{ editor_choice.thumbnail_url }}{% elif editor_choice.thumbnail %}{{ editor_choice.thumbnail.url }}{% else %}{% static 'assets/img/editor.jpg' %}{% endif %}"
                             alt="{{ editor_choice.title }}" class="live-image"
                             onerror="this.src='{% static 'assets/img/editor.jpg' %}'"
                             loading="lazy">
                        <h5 class="live-title">
                            <a href="{{ editor_choice.get_absolute_url }}">{{ editor_choice.title|truncatechars:80 }}</a>
                        </h5>
                    </div>
                    {% endif %}
                </div>

                <!-- Enhanced Latest News Section -->
                <div class="latest-news-section mt-4">
                    <div class="section-header">
                        <h3 class="section-title">
                            <i class="fas fa-clock"></i>Latest Updates
                        </h3>
                        <div class="live-indicator">
                            <span class="live-dot"></span>
                            <span class="live-text">LIVE</span>
                        </div>
                    </div>

                    <!-- Breaking News Alert -->
                    {% if breaking_news_items %}
                    <div class="breaking-alert mb-3">
                        <div class="breaking-header">
                            <i class="fas fa-exclamation-triangle"></i>
                            <span>BREAKING</span>
                        </div>
                        {% for news in breaking_news_items|slice:":2" %}
                        <div class="breaking-item">
                            <a href="{{ news.get_absolute_url }}" class="breaking-link">
                                {{ news.title|truncatechars:80 }}
                            </a>
                            <span class="breaking-time">{{ news.timestamp|timesince }} ago</span>
                        </div>
                        {% endfor %}
                    </div>
                    {% endif %}

                    <!-- Recent Updates (Last 24h) -->
                    <div class="news-stack">
                        <div class="stack-header">
                            <h5 class="stack-title">
                                <i class="fas fa-newspaper"></i>Recent Updates
                            </h5>
                            <span class="update-count">{{ recent_updates|length }} new</span>
                        </div>

                        {% for news in recent_updates|slice:":6" %}
                        <div class="latest-news-item {% if news.priority == 'high' %}priority-high{% elif news.is_trending %}trending{% endif %}">
                            <div class="news-priority">
                                {% if news.priority == 'high' %}
                                    <span class="priority-badge high">HIGH</span>
                                {% elif news.is_trending %}
                                    <span class="priority-badge trending">
                                        <i class="fas fa-fire"></i>TRENDING
                                    </span>
                                {% elif news.is_featured %}
                                    <span class="priority-badge featured">FEATURED</span>
                                {% endif %}
                            </div>

                            <div class="news-content">
                                <div class="news-meta">
                                    <span class="news-category">{{ news.category.name }}</span>
                                    <span class="news-time">{{ news.timestamp|date:"g:i A" }}</span>
                                    <span class="time-ago">{{ news.timestamp|timesince }} ago</span>
                                </div>

                                <h6 class="latest-news-title">
                                    <a href="{{ news.get_absolute_url }}">{{ news.title|truncatechars:85 }}</a>
                                </h6>

                                <div class="news-engagement">
                                    <span class="engagement-item">
                                        <i class="fas fa-eye"></i>{{ news.views_count }}
                                    </span>
                                    <span class="engagement-item">
                                        <i class="fas fa-comments"></i>{{ news.get_comment_count }}
                                    </span>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>

                    <!-- Most Viewed This Week -->
                    {% if most_viewed_week %}
                    <div class="trending-stack mt-4">
                        <div class="stack-header">
                            <h5 class="stack-title">
                                <i class="fas fa-chart-line"></i>Most Viewed This Week
                            </h5>
                        </div>

                        {% for news in most_viewed_week|slice:":3" %}
                        <div class="trending-item">
                            <div class="trending-rank">{{ forloop.counter }}</div>
                            <div class="trending-content">
                                <h6 class="trending-title">
                                    <a href="{{ news.get_absolute_url }}">{{ news.title|truncatechars:70 }}</a>
                                </h6>
                                <div class="trending-stats">
                                    <span><i class="fas fa-eye"></i>{{ news.views_count }} views</span>
                                    <span><i class="fas fa-calendar"></i>{{ news.timestamp|date:"M j" }}</span>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    {% endif %}
                </div>

                <!-- Right Sidebar Ad -->
                {% if has_sidebar_ads %}
                <div class="sidebar-ad-section mt-4">
                    {% display_ad "sidebar" "300x250" "right-sidebar-ad" %}
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</section>

<!-- Horizontal Banner Advertisement -->
{% has_active_ads "banner" "970x250" as has_banner_ads %}
{% if has_banner_ads %}
<section class="horizontal-banner-ad py-4">
    <div class="container">
        <div class="horizontal-ad-banner large">
            {% display_ad "banner" "970x250" "billboard-ad" %}
        </div>
    </div>
</section>
{% endif %}

<!-- Category News Sections -->
<section class="category-news-sections py-5">
    <div class="container">
        <!-- Politics Section -->
        {% if post_catalog_one %}
        <div class="category-section mb-5">
            <div class="row">
                <div class="col-md-8">
                    {% for post in post_catalog_one %}
                    {% if forloop.first %}
                    <div class="category-header mb-4">
                        <h2 class="category-title">
                            <a href="{{ post.category.get_absolute_url }}" class="text-decoration-none">
                                {{ post.category.name }}
                            </a>
                        </h2>
                        <div class="category-line"></div>
                    </div>
                    {% endif %}

                    <div class="category-main-story mb-4">
                        <div class="row">
                            <div class="col-md-5">
                                <div class="category-image">
                                    <a href="{{ post.get_absolute_url }}">
                                        <img src="{% if post.thumbnail_url %}{{ post.thumbnail_url }}{% elif post.thumbnail %}{{ post.thumbnail.url }}{% else %}{% static 'assets/img/category_img3.jpg' %}{% endif %}"
                                             alt="{{ post.title }}" class="img-fluid rounded"
                                             onerror="this.src='{% static 'assets/img/category_img3.jpg' %}'"
                                             loading="lazy">
                                    </a>
                                </div>
                            </div>
                            <div class="col-md-7">
                                <div class="category-content">
                                    {% for tag in post.tags.all %}
                                    <span class="tag-badge">{{ tag }}</span>
                                    {% endfor %}

                                    <h3 class="category-story-title">
                                        <a href="{{ post.get_absolute_url }}">{{ post.title }}</a>
                                    </h3>

                                    <div class="story-meta mb-2">
                                        <span class="meta-date">{{ post.timestamp|date:"M j, Y" }}</span>
                                        <span class="meta-author">by {{ post.author|title }}</span>
                                    </div>

                                    <p class="story-excerpt">{{ post.description|truncatechars:150 }}</p>

                                    <div class="story-engagement">
                                        <span class="engagement-item">
                                            <i class="fas fa-share-alt me-1"></i>424 Shares
                                        </span>
                                        <span class="engagement-item">
                                            <i class="fas fa-comments me-1"></i>{{ post.get_comment_count }} Comments
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    {% if forloop.last %}
                    <div class="category-more">
                        <a href="{{ post.category.get_absolute_url }}" class="more-link">
                            More {{ post.category.name }} News <i class="fas fa-arrow-right ms-1"></i>
                        </a>
                    </div>
                    {% endif %}
                    {% endfor %}
                </div>

                <!-- Category Sidebar -->
                <div class="col-md-4">
                    <div class="category-sidebar">
                        <!-- Related Stories -->
                        <div class="related-stories">
                            <h4 class="sidebar-title">Related Stories</h4>
                            {% for news in latest_news|slice:":3" %}
                            <div class="related-story-item">
                                <h6 class="related-story-title">
                                    <a href="{{ news.get_absolute_url }}">{{ news.title|truncatechars:60 }}</a>
                                </h6>
                                <small class="text-muted">{{ news.timestamp|timesince }} ago</small>
                            </div>
                            {% endfor %}
                        </div>

                        <!-- Sidebar Ad -->
                        {% if has_sidebar_ads %}
                        <div class="sidebar-ad mt-4">
                            {% display_ad "sidebar" "300x250" "category-sidebar-ad" %}
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
        {% endif %}
    </div>
</section>

<!-- Technology Section -->
{% if post_catalog_two %}
<section class="technology-section py-5 bg-light">
    <div class="container">
        <div class="row">
            <div class="col-md-8">
                {% for post in post_catalog_two %}
                {% if forloop.first %}
                <div class="category-header mb-4">
                    <h2 class="category-title tech">
                        <a href="{{ post.category.get_absolute_url }}" class="text-decoration-none">
                            {{ post.category.name }}
                        </a>
                    </h2>
                    <div class="category-line tech"></div>
                </div>
                {% endif %}

                <div class="tech-story-grid">
                    <div class="row">
                        <div class="col-md-6 mb-4">
                            <div class="tech-story-card">
                                <div class="tech-image">
                                    <a href="{{ post.get_absolute_url }}">
                                        <img src="{% if post.thumbnail_url %}{{ post.thumbnail_url }}{% elif post.thumbnail %}{{ post.thumbnail.url }}{% else %}{% static 'assets/img/tech_photo1.jpg' %}{% endif %}"
                                             alt="{{ post.title }}" class="img-fluid rounded"
                                             onerror="this.src='{% static 'assets/img/tech_photo1.jpg' %}'"
                                             loading="lazy">
                                    </a>
                                </div>

                                <div class="tech-content mt-3">
                                    {% for tag in post.tags.all %}
                                    <span class="tag-badge tech">{{ tag }}</span>
                                    {% endfor %}

                                    <h4 class="tech-story-title">
                                        <a href="{{ post.get_absolute_url }}">{{ post.title }}</a>
                                    </h4>

                                    <div class="story-meta mb-2">
                                        <span class="meta-date">{{ post.timestamp|date:"M j, Y" }}</span>
                                        <span class="meta-author">by {{ post.author|title }}</span>
                                    </div>

                                    <p class="story-excerpt">{{ post.description|truncatechars:120 }}</p>

                                    <div class="story-engagement">
                                        <span class="engagement-item">
                                            <i class="fas fa-share-alt me-1"></i>{{ post.get_comment_count }} Comments
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                {% if forloop.last %}
                <div class="category-more">
                    <a href="{{ post.category.get_absolute_url }}" class="more-link tech">
                        More {{ post.category.name }} News <i class="fas fa-arrow-right ms-1"></i>
                    </a>
                </div>
                {% endif %}
                {% endfor %}
            </div>

            <!-- Tech Sidebar -->
            <div class="col-md-4">
                <div class="tech-sidebar">
                    <!-- Trending Tech -->
                    <div class="trending-tech">
                        <h4 class="sidebar-title">Trending in Tech</h4>
                        {% for news in latest_news|slice:"2:5" %}
                        <div class="trending-tech-item">
                            <h6 class="trending-tech-title">
                                <a href="{{ news.get_absolute_url }}">{{ news.title|truncatechars:55 }}</a>
                            </h6>
                            <small class="text-muted">{{ news.timestamp|timesince }} ago</small>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
{% endif %}
<!-- Enhanced News Display Section -->
<section class="enhanced-news-display py-5 bg-light">
    <div class="container">
        <!-- News Statistics Bar -->
        <div class="news-stats-bar mb-4">
            <div class="row text-center">
                <div class="col-md-3">
                    <div class="stat-item">
                        <div class="stat-number">{{ total_news_count }}</div>
                        <div class="stat-label">Total Articles</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-item">
                        <div class="stat-number">{{ total_categories }}</div>
                        <div class="stat-label">Categories</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-item">
                        <div class="stat-number">{{ todays_news|length }}</div>
                        <div class="stat-label">Today's News</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-item">
                        <div class="stat-number">{{ api_news|length }}</div>
                        <div class="stat-label">Live Updates</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Today's News Section -->
        {% if todays_news %}
        <div class="todays-news-section mb-5">
            <h3 class="section-title">
                <i class="fas fa-calendar-day"></i>Today's Headlines
                <span class="news-count-badge">{{ todays_news|length }}</span>
            </h3>
            <div class="row">
                {% for news in todays_news|slice:":6" %}
                <div class="col-lg-4 col-md-6 mb-3">
                    <div class="today-news-card">
                        <div class="news-image">
                            <img src="{% if news.thumbnail_url %}{{ news.thumbnail_url }}{% elif news.thumbnail %}{{ news.thumbnail.url }}{% else %}{% static 'assets/img/category_img1.jpg' %}{% endif %}"
                                 alt="{{ news.title }}"
                                 onerror="this.src='{% static 'assets/img/category_img1.jpg' %}'"
                                 loading="lazy">
                            <div class="news-category-badge">{{ news.category.name }}</div>
                            {% if news.is_breaking %}
                            <div class="breaking-badge">BREAKING</div>
                            {% endif %}
                        </div>
                        <div class="news-content">
                            <h6 class="news-title">
                                <a href="{{ news.get_absolute_url }}">{{ news.title|truncatechars:80 }}</a>
                            </h6>
                            <p class="news-excerpt">{{ news.description|truncatechars:100 }}</p>
                            <div class="news-meta">
                                <span class="news-time">
                                    <i class="fas fa-clock"></i>{{ news.timestamp|timesince }} ago
                                </span>
                                <span class="news-author">
                                    <i class="fas fa-user"></i>{{ news.author.user.get_full_name|default:news.author.user.username }}
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
        {% endif %}

        <!-- API News Section -->
        {% if api_news %}
        <div class="api-news-section mb-5">
            <h3 class="section-title">
                <i class="fas fa-rss"></i>Live News Updates
                <span class="live-badge">LIVE</span>
            </h3>
            <div class="row">
                {% for news in api_news|slice:":8" %}
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="api-news-card">
                        <div class="news-image">
                            <img src="{% if news.thumbnail_url %}{{ news.thumbnail_url }}{% elif news.thumbnail %}{{ news.thumbnail.url }}{% else %}{% static 'assets/img/category_img2.jpg' %}{% endif %}"
                                 alt="{{ news.title }}"
                                 onerror="this.src='{% static 'assets/img/category_img2.jpg' %}'"
                                 loading="lazy">
                            <div class="api-source-badge">NewsData.io</div>
                        </div>
                        <div class="news-content">
                            <h6 class="news-title">
                                <a href="{{ news.get_absolute_url }}">{{ news.title|truncatechars:60 }}</a>
                            </h6>
                            <div class="news-meta">
                                <span class="news-category">{{ news.category.name }}</span>
                                <span class="news-time">{{ news.timestamp|timesince }} ago</span>
                            </div>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
        {% endif %}

        <!-- Dynamic Category Grid -->
        {% if latest_by_category %}
        <div class="dynamic-categories-section">
            <h3 class="section-title">
                <i class="fas fa-th-large"></i>News by Category
            </h3>
            <div class="row">
                {% for category_name, category_data in latest_by_category.items %}
                <div class="col-lg-6 col-xl-4 mb-4">
                    <div class="category-card">
                        <div class="category-header">
                            <h5 class="category-title">
                                <i class="fas fa-{% if category_name == 'Politics' %}landmark{% elif category_name == 'Technology' %}microchip{% elif category_name == 'Sports' %}futbol{% elif category_name == 'Business' %}chart-line{% elif category_name == 'Health' %}heartbeat{% elif category_name == 'World' %}globe{% else %}newspaper{% endif %}"></i>
                                {{ category_name }}
                                <span class="category-count">({{ category_data.count }})</span>
                            </h5>
                            <a href="{% url 'newspaper:category' category_data.category.slug %}" class="view-all-btn">
                                View All
                            </a>
                        </div>

                        <div class="category-news-list">
                            {% for news in category_data.news|slice:":3" %}
                            <div class="category-news-item">
                                <div class="news-thumbnail">
                                    <img src="{% if news.thumbnail_url %}{{ news.thumbnail_url }}{% elif news.thumbnail %}{{ news.thumbnail.url }}{% else %}{% static 'assets/img/category_img3.jpg' %}{% endif %}"
                                         alt="{{ news.title }}"
                                         onerror="this.src='{% static 'assets/img/category_img3.jpg' %}'"
                                         loading="lazy">
                                </div>
                                <div class="news-info">
                                    <h6 class="news-title">
                                        <a href="{{ news.get_absolute_url }}">{{ news.title|truncatechars:50 }}</a>
                                    </h6>
                                    <div class="news-meta">
                                        <span class="news-time">{{ news.timestamp|timesince }} ago</span>
                                        {% if news.is_featured %}
                                        <span class="featured-tag">Featured</span>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
        {% endif %}
    </div>
</section>

<!-- Bottom Horizontal Advertisement -->
{% has_active_ads "footer" "728x90" as has_footer_ads %}
{% if has_footer_ads %}
<section class="bottom-banner-ad py-4">
    <div class="container">
        <div class="horizontal-ad-banner">
            {% display_ad "footer" "728x90" "footer-ad" %}
        </div>
    </div>
</section>
{% endif %}

{% endblock %}
