{% extends 'site/base.html' %}
{% load static %}

{% block title %}Sign Up{% endblock %}

{% block extra_css %}
<style>
.auth-container {
    min-height: 80vh;
    display: flex;
    align-items: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 2rem 0;
}

.auth-card {
    background: white;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    max-width: 900px;
    margin: 0 auto;
}

.auth-left {
    background: var(--gradient-secondary);
    color: white;
    padding: 3rem;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.auth-left::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="2" fill="rgba(255,255,255,0.1)"/></svg>') repeat;
    animation: float 20s infinite linear;
}

@keyframes float {
    0% { transform: translate(-50%, -50%) rotate(0deg); }
    100% { transform: translate(-50%, -50%) rotate(360deg); }
}

.auth-right {
    padding: 3rem;
}

.auth-title {
    font-size: 2.5rem;
    font-weight: 800;
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.auth-subtitle {
    color: var(--text-secondary);
    margin-bottom: 2rem;
    font-size: 1.1rem;
}

.form-floating {
    margin-bottom: 1.5rem;
}

.form-floating .form-control {
    border: 2px solid #e9ecef;
    border-radius: 12px;
    padding: 1rem 0.75rem;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: #f8f9fa;
}

.form-floating .form-control:focus {
    border-color: var(--secondary-color);
    box-shadow: 0 0 0 0.2rem rgba(209, 52, 56, 0.25);
    background: white;
}

.form-floating label {
    color: var(--text-muted);
    font-weight: 500;
}

.btn-auth {
    background: var(--gradient-secondary);
    border: none;
    color: white;
    padding: 1rem 2rem;
    border-radius: 12px;
    font-weight: 600;
    font-size: 1.1rem;
    width: 100%;
    transition: all 0.3s ease;
    margin-bottom: 1.5rem;
}

.btn-auth:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(209, 52, 56, 0.3);
    color: white;
}

.auth-link {
    color: var(--secondary-color);
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
}

.auth-link:hover {
    color: var(--primary-dark);
    text-decoration: underline;
}

.alert-modern {
    border: none;
    border-radius: 12px;
    padding: 1rem 1.5rem;
    margin-bottom: 1.5rem;
    border-left: 4px solid var(--secondary-color);
}

.feature-list {
    list-style: none;
    padding: 0;
}

.feature-list li {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
    font-size: 1.1rem;
}

.feature-list i {
    margin-right: 0.75rem;
    font-size: 1.2rem;
}

@media (max-width: 768px) {
    .auth-left {
        padding: 2rem;
        min-height: 200px;
    }

    .auth-right {
        padding: 2rem;
    }

    .auth-title {
        font-size: 2rem;
    }

    .form-floating {
        margin-bottom: 1rem;
    }
}
</style>
{% endblock %}

{% block content %}
<section class="auth-container">
    <div class="container">
        <div class="auth-card">
            <div class="row g-0">
                <div class="col-lg-5 d-none d-lg-block">
                    <div class="auth-left">
                        <div class="position-relative z-index-1">
                            <div class="mb-4">
                                <i class="fas fa-user-plus fa-4x mb-3"></i>
                                <h3 class="fw-bold">Join NBC News</h3>
                                <p class="mb-0">Create your account and become part of our news community.</p>
                            </div>
                            <ul class="feature-list text-start">
                                <li>
                                    <i class="fas fa-bell"></i>
                                    <span>Get breaking news alerts</span>
                                </li>
                                <li>
                                    <i class="fas fa-bookmark"></i>
                                    <span>Save articles to read later</span>
                                </li>
                                <li>
                                    <i class="fas fa-comments"></i>
                                    <span>Join the conversation</span>
                                </li>
                                <li>
                                    <i class="fas fa-heart"></i>
                                    <span>Personalized content</span>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="col-lg-7">
                    <div class="auth-right">
                        <div class="text-center mb-4 d-lg-none">
                            <i class="fas fa-user-plus fa-3x text-danger mb-3"></i>
                        </div>

                        <h2 class="auth-title">Create Account</h2>
                        <p class="auth-subtitle">Fill in your information to get started</p>

                        <!-- Error Messages -->
                        {% if form.errors %}
                            {% for field in form %}
                                {% for error in field.errors %}
                                    <div class="alert alert-danger alert-modern">
                                        <i class="fas fa-exclamation-triangle me-2"></i>
                                        <strong>{{ error|escape }}</strong>
                                    </div>
                                {% endfor %}
                            {% endfor %}
                            {% for error in form.non_field_errors %}
                                <div class="alert alert-danger alert-modern">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    <strong>{{ error|escape }}</strong>
                                </div>
                            {% endfor %}
                        {% endif %}

                        <form method="POST" action="">
                            {% csrf_token %}
                            {% for field in form %}
                                <div class="form-floating">
                                    <input type="{{ field.field.widget.input_type }}"
                                           class="form-control"
                                           name="{{ field.name }}"
                                           id="id_{{ field.name }}"
                                           placeholder="{{ field.field.widget.attrs.placeholder }}"
                                           required>
                                    <label for="id_{{ field.name }}">{{ field.field.widget.attrs.placeholder }}</label>
                                </div>
                            {% endfor %}

                            <button class="btn btn-auth" type="submit">
                                <i class="fas fa-user-plus me-2"></i>Create Account
                            </button>
                        </form>

                        <div class="text-center">
                            <p class="mb-0">Already have an account?
                                <a href="{% url 'account:login' %}" class="auth-link">Sign in here</a>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
{% endblock %}