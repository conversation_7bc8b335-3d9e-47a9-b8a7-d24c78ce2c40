{% load static %}

<!-- Breaking News Banner -->
{% if breaking_news %}
<div class="breaking-news-banner">
    <div class="container-fluid">
        <div class="row align-items-center">
            <div class="col-auto">
                <span class="badge bg-white text-danger fw-bold px-3 py-2">
                    <i class="fas fa-exclamation-triangle me-1"></i>BREAKING
                </span>
            </div>
            <div class="col">
                <div class="ticker">
                    {% for news in breaking_news %}
                        <span class="me-5">
                            <strong>{{ news.title }}</strong> - {{ news.content|truncatechars:100 }}
                        </span>
                    {% endfor %}
                </div>
            </div>
            <div class="col-auto">
                <button class="btn btn-sm btn-outline-light" onclick="closeBreakingNews()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
    </div>
</div>
{% endif %}

<script>
function closeBreakingNews() {
    const banner = document.querySelector('.breaking-news-banner');
    if (banner) {
        banner.style.animation = 'slideUp 0.5s ease-out forwards';
        setTimeout(() => {
            banner.style.display = 'none';
        }, 500);
    }
}

// Auto-hide breaking news after 30 seconds
setTimeout(() => {
    const banner = document.querySelector('.breaking-news-banner');
    if (banner) {
        banner.style.opacity = '0.8';
    }
}, 30000);

// CSS for slide up animation
const style = document.createElement('style');
style.textContent = `
    @keyframes slideUp {
        from { transform: translateY(0); }
        to { transform: translateY(-100%); }
    }
`;
document.head.appendChild(style);
</script>
