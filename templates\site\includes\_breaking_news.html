{% load static %}

<!-- Enhanced Breaking News Banner -->
{% if breaking_news or breaking_news_items %}
<div class="breaking-news-banner" id="breakingNewsBanner">
    <div class="container-fluid">
        <div class="row align-items-center">
            <div class="col-auto">
                <span class="badge bg-white text-danger fw-bold px-3 py-2">
                    <i class="fas fa-exclamation-triangle me-1"></i>BREAKING
                </span>
            </div>
            <div class="col">
                <div class="ticker" id="breakingNewsTicker">
                    {% if breaking_news %}
                        {% for news in breaking_news %}
                            <span class="me-5" data-breaking="true">
                                <strong>{{ news.title }}</strong>
                                {% if news.content %} - {{ news.content|truncatechars:100 }}{% endif %}
                            </span>
                        {% endfor %}
                    {% endif %}
                    {% if breaking_news_items %}
                        {% for news in breaking_news_items %}
                            <span class="me-5" data-breaking="true">
                                <strong>{{ news.title }}</strong>
                                {% if news.description %} - {{ news.description|truncatechars:100 }}{% endif %}
                            </span>
                        {% endfor %}
                    {% endif %}
                </div>
            </div>
            <div class="col-auto">
                <button class="btn btn-sm btn-outline-light" onclick="closeBreakingNews()" title="Close breaking news">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
    </div>
</div>
{% else %}
<!-- Hidden banner for dynamic content -->
<div class="breaking-news-banner" id="breakingNewsBanner" style="display: none;">
    <div class="container-fluid">
        <div class="row align-items-center">
            <div class="col-auto">
                <span class="badge bg-white text-danger fw-bold px-3 py-2">
                    <i class="fas fa-exclamation-triangle me-1"></i>BREAKING
                </span>
            </div>
            <div class="col">
                <div class="ticker" id="breakingNewsTicker">
                    <!-- Dynamic content will be inserted here -->
                </div>
            </div>
            <div class="col-auto">
                <button class="btn btn-sm btn-outline-light" onclick="closeBreakingNews()" title="Close breaking news">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
    </div>
</div>
{% endif %}

<script>
function closeBreakingNews() {
    const banner = document.getElementById('breakingNewsBanner');
    if (banner) {
        banner.style.animation = 'slideUp 0.5s ease-out forwards';
        setTimeout(() => {
            banner.style.display = 'none';
            // Store in localStorage to remember user preference
            localStorage.setItem('breakingNewsClosed', 'true');
        }, 500);
    }
}

// Enhanced breaking news management
document.addEventListener('DOMContentLoaded', function() {
    // Check if user previously closed breaking news
    const wasClosed = localStorage.getItem('breakingNewsClosed');
    const banner = document.getElementById('breakingNewsBanner');

    if (wasClosed === 'true' && banner) {
        banner.style.display = 'none';
    }

    // Auto-fade breaking news after 45 seconds (but don't hide completely)
    setTimeout(() => {
        if (banner && banner.style.display !== 'none') {
            banner.style.opacity = '0.85';
            banner.style.transition = 'opacity 2s ease';
        }
    }, 45000);

    // Reset the closed state after 1 hour
    setTimeout(() => {
        localStorage.removeItem('breakingNewsClosed');
    }, 3600000); // 1 hour
});

// Enhanced CSS animations
const style = document.createElement('style');
style.textContent = `
    @keyframes slideUp {
        from {
            transform: translateY(0);
            opacity: 1;
        }
        to {
            transform: translateY(-100%);
            opacity: 0;
        }
    }

    .breaking-news-banner {
        transition: opacity 0.3s ease;
    }

    .breaking-news-banner:hover {
        opacity: 1 !important;
    }
`;
document.head.appendChild(style);

// Function to manually refresh breaking news
function refreshBreakingNews() {
    if (typeof checkBreakingNews === 'function') {
        checkBreakingNews();
    }
}

// Add refresh button functionality
document.addEventListener('DOMContentLoaded', function() {
    const banner = document.getElementById('breakingNewsBanner');
    if (banner) {
        banner.addEventListener('dblclick', refreshBreakingNews);
    }
});
</script>
