{% load static %}

<div class="col-md-7">
    <div class="feature_article_wrapper">
        <div class="feature_article_img">
            <img style="height: 570px; width: 652.5px;" class="img-responsive top_static_article_img"
                src="{% if hot_news.thumbnail_url %}{{ hot_news.thumbnail_url }}{% elif hot_news.thumbnail %}{{ hot_news.thumbnail.url }}{% else %}{% static 'assets/img/feature-top.jpg' %}{% endif %}"
                alt="{{ hot_news.title }}"
                onerror="this.src='{% static 'assets/img/feature-top.jpg' %}'"
                loading="lazy">
        </div>
        <!-- feature_article_img -->

        <div class="feature_article_inner">
            <div class="tag_lg red">Hot News</div>
            <div class="feature_article_title">
                <h1><a href="{{ hot_news.get_absolute_url }}" target="_self">{{ hot_news.title }}</a></h1>
            </div>
            <!-- feature_article_title -->

            <div class="feature_article_date"><a href="#" target="_self">{{ hot_news.author|title }}</a> , <a href="#"
                    target="_self">{{ hot_news.timestamp|date:"j M Y" }}</a></div>
            <!-- feature_article_date -->

            <div class="feature_article_content">
                {{ hot_news.description|truncatechars:100 }}
            </div>
            <!-- feature_article_content -->

            <div class="article_social">
                <span><i class="fa fa-share-alt"></i><a href="#">424</a>Shares</span>
                <span><i class="fa fa-comments-o"></i><a href="#">{{ hot_news.get_comment_count }}</a>Comments</span>
            </div>
            <!-- article_social -->

        </div>
        <!-- feature_article_inner -->

    </div>
    <!-- feature_article_wrapper -->

</div>
