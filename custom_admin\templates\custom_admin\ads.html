{% extends 'custom_admin/base.html' %}

{% block title %}Advertisement Management{% endblock %}
{% block page_title %}Advertisement Management{% endblock %}

{% block content %}
<!-- Ad Statistics -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stat-card">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">Total Ads</div>
                        <div class="h5 mb-0 font-weight-bold">{{ advertisements.count }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-ad fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stat-card-success">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">Active Ads</div>
                        <div class="h5 mb-0 font-weight-bold" id="active-ads-count">0</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-check-circle fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stat-card-info">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">Total Clicks</div>
                        <div class="h5 mb-0 font-weight-bold" id="total-clicks">0</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-mouse-pointer fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stat-card-warning">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">Total Impressions</div>
                        <div class="h5 mb-0 font-weight-bold" id="total-impressions">0</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-eye fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Advertisement Management</h5>
                    <div>
                        <a href="/admin/news/advertisement/add/" class="btn btn-modern me-2">
                            <i class="fas fa-plus"></i> Create New Ad
                        </a>
                        <button class="btn btn-outline-primary me-2" onclick="refreshAdStats()">
                            <i class="fas fa-sync"></i> Refresh Stats
                        </button>
                        <button class="btn btn-outline-success" onclick="testAdRotation()">
                            <i class="fas fa-random"></i> Test Ad Rotation
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Ad Zones Preview -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-desktop"></i> Ad Zones Preview</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <!-- Banner Zone -->
                    <div class="col-md-6 mb-3">
                        <h6>Banner Ads (728x90)</h6>
                        <div class="ad-preview-zone" style="height: 90px; border: 2px dashed #ccc; display: flex; align-items: center; justify-content: center; background: #f8f9fa;">
                            <div id="banner-preview">
                                <small class="text-muted">Banner Ad Zone</small>
                            </div>
                        </div>
                        <button class="btn btn-sm btn-outline-primary mt-2" onclick="loadAdPreview('banner')">
                            <i class="fas fa-eye"></i> Preview Banner Ad
                        </button>
                    </div>

                    <!-- Sidebar Zone -->
                    <div class="col-md-6 mb-3">
                        <h6>Sidebar Ads (300x250)</h6>
                        <div class="ad-preview-zone" style="height: 250px; width: 300px; border: 2px dashed #ccc; display: flex; align-items: center; justify-content: center; background: #f8f9fa;">
                            <div id="sidebar-preview">
                                <small class="text-muted">Sidebar Ad Zone</small>
                            </div>
                        </div>
                        <button class="btn btn-sm btn-outline-primary mt-2" onclick="loadAdPreview('sidebar')">
                            <i class="fas fa-eye"></i> Preview Sidebar Ad
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Advertisements Table -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-list"></i> All Advertisements</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Title</th>
                                <th>Type</th>
                                <th>Size</th>
                                <th>Status</th>
                                <th>Clicks</th>
                                <th>Impressions</th>
                                <th>CTR</th>
                                <th>Schedule</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for ad in advertisements %}
                            <tr data-ad-id="{{ ad.id }}">
                                <td>
                                    <strong>{{ ad.title }}</strong>
                                    {% if ad.url %}
                                    <br><small class="text-muted">{{ ad.url|truncatechars:40 }}</small>
                                    {% endif %}
                                </td>
                                <td>
                                    <span class="badge bg-primary">{{ ad.ad_type|title }}</span>
                                </td>
                                <td>
                                    <span class="badge bg-secondary">{{ ad.ad_size }}</span>
                                </td>
                                <td>
                                    {% if ad.is_active %}
                                        <span class="badge bg-success">Active</span>
                                    {% else %}
                                        <span class="badge bg-danger">Inactive</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <strong>{{ ad.clicks }}</strong>
                                </td>
                                <td>
                                    <strong>{{ ad.impressions }}</strong>
                                </td>
                                <td>
                                    {% if ad.impressions > 0 %}
                                        <strong>{% widthratio ad.clicks ad.impressions 100 %}%</strong>
                                    {% else %}
                                        <span class="text-muted">0%</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <small>
                                        <strong>Start:</strong> {{ ad.start_date|date:"M d, Y" }}<br>
                                        {% if ad.end_date %}
                                        <strong>End:</strong> {{ ad.end_date|date:"M d, Y" }}
                                        {% else %}
                                        <span class="text-muted">No end date</span>
                                        {% endif %}
                                    </small>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="/admin/news/advertisement/{{ ad.id }}/change/" class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <button class="btn btn-sm btn-outline-info" onclick="previewAd({{ ad.id }})">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="btn btn-sm btn-outline-success" onclick="toggleAdStatus({{ ad.id }}, {{ ad.is_active|yesno:'true,false' }})">
                                            {% if ad.is_active %}
                                                <i class="fas fa-pause"></i>
                                            {% else %}
                                                <i class="fas fa-play"></i>
                                            {% endif %}
                                        </button>
                                        <button class="btn btn-sm btn-outline-warning" onclick="resetAdStats({{ ad.id }})">
                                            <i class="fas fa-redo"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Ad Preview Modal -->
<div class="modal fade" id="adPreviewModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Advertisement Preview</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="ad-preview-content" style="text-align: center; padding: 20px;">
                    <!-- Ad content will be loaded here -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" onclick="simulateAdClick()">Simulate Click</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let currentPreviewAdId = null;

document.addEventListener('DOMContentLoaded', function() {
    calculateAdStats();
});

function calculateAdStats() {
    let activeAds = 0;
    let totalClicks = 0;
    let totalImpressions = 0;
    
    {% for ad in advertisements %}
        {% if ad.is_active %}
            activeAds++;
        {% endif %}
        totalClicks += {{ ad.clicks }};
        totalImpressions += {{ ad.impressions }};
    {% endfor %}
    
    document.getElementById('active-ads-count').textContent = activeAds;
    document.getElementById('total-clicks').textContent = totalClicks.toLocaleString();
    document.getElementById('total-impressions').textContent = totalImpressions.toLocaleString();
}

function toggleAdStatus(adId, isActive) {
    fetch(`{% url 'custom_admin:toggle_ad' 0 %}`.replace('0', adId), {
        method: 'POST',
        headers: {
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
            'Content-Type': 'application/json',
        },
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('Error updating ad status');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error updating ad status');
    });
}

function previewAd(adId) {
    currentPreviewAdId = adId;
    
    // Find the ad data from the table
    const row = document.querySelector(`tr[data-ad-id="${adId}"]`);
    const title = row.querySelector('td:first-child strong').textContent;
    
    document.getElementById('ad-preview-content').innerHTML = `
        <h4>${title}</h4>
        <p class="text-muted">Ad preview would be displayed here</p>
        <div class="alert alert-info">
            <i class="fas fa-info-circle"></i> 
            This is a preview of how the ad would appear on the website.
        </div>
    `;
    
    const modal = new bootstrap.Modal(document.getElementById('adPreviewModal'));
    modal.show();
}

function loadAdPreview(adType) {
    fetch(`/api/ads/${adType}/`)
        .then(response => response.json())
        .then(data => {
            if (data.success && data.ad) {
                const previewElement = document.getElementById(`${adType}-preview`);
                if (data.ad.html_content) {
                    previewElement.innerHTML = data.ad.html_content;
                } else {
                    previewElement.innerHTML = `<small class="text-muted">No ${adType} ad available</small>`;
                }
            } else {
                document.getElementById(`${adType}-preview`).innerHTML = `<small class="text-muted">No ${adType} ad available</small>`;
            }
        })
        .catch(error => {
            console.error('Error loading ad preview:', error);
            document.getElementById(`${adType}-preview`).innerHTML = `<small class="text-danger">Error loading ad</small>`;
        });
}

function simulateAdClick() {
    if (currentPreviewAdId) {
        fetch(`/api/ads/click/${currentPreviewAdId}/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Ad click simulated successfully!');
                location.reload();
            }
        })
        .catch(error => {
            console.error('Error simulating click:', error);
        });
    }
}

function resetAdStats(adId) {
    if (confirm('Are you sure you want to reset the statistics for this ad?')) {
        // Here you would implement the reset stats API call
        console.log('Resetting stats for ad:', adId);
        alert('Ad statistics reset (demo)');
    }
}

function refreshAdStats() {
    location.reload();
}

function testAdRotation() {
    alert('Testing ad rotation - this would cycle through different ads in each zone');
    loadAdPreview('banner');
    setTimeout(() => loadAdPreview('sidebar'), 1000);
}
</script>
{% endblock %}
