# Generated by Django 3.1.4 on 2021-04-14 09:45

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('news', '0008_news_author'),
        ('mainsite', '0006_auto_20210414_1523'),
    ]

    operations = [
        migrations.CreateModel(
            name='MenuSettings',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('menu_five', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='menu_five', to='news.category')),
                ('menu_four', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='menu_four', to='news.category')),
                ('menu_one', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='menu_one', to='news.category')),
                ('menu_six', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='menu_six', to='news.category')),
                ('menu_three', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='menu_three', to='news.category')),
                ('menu_two', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='menu_two', to='news.category')),
            ],
            options={
                'verbose_name': 'Menu Setting',
                'verbose_name_plural': 'Menu Settings',
                'db_table': 'menusettings',
            },
        ),
    ]
