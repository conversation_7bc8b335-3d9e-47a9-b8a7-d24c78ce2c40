{% extends 'custom_admin/base.html' %}

{% block title %}Analytics{% endblock %}
{% block page_title %}Analytics Dashboard{% endblock %}

{% block content %}
<!-- Analytics Overview Cards -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stat-card">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">Total Page Views</div>
                        <div class="h5 mb-0 font-weight-bold" id="total-views">Loading...</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-eye fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stat-card-success">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">Ad Clicks</div>
                        <div class="h5 mb-0 font-weight-bold" id="total-ad-clicks">Loading...</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-mouse-pointer fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stat-card-info">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">Ad Impressions</div>
                        <div class="h5 mb-0 font-weight-bold" id="total-impressions">Loading...</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-chart-bar fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stat-card-warning">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">Average CTR</div>
                        <div class="h5 mb-0 font-weight-bold" id="average-ctr">Loading...</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-percentage fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Charts Row -->
<div class="row mb-4">
    <!-- News by Category Chart -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-chart-pie"></i> News by Category</h5>
            </div>
            <div class="card-body">
                <canvas id="categoryChart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>

    <!-- Ad Performance Chart -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-chart-line"></i> Ad Performance</h5>
            </div>
            <div class="card-body">
                <canvas id="adPerformanceChart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- Top Articles and Ad Performance Tables -->
<div class="row">
    <!-- Top Articles -->
    <div class="col-lg-8 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-trophy"></i> Top Performing Articles</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Article</th>
                                <th>Category</th>
                                <th>Views</th>
                                <th>Published</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for article in top_articles %}
                            <tr>
                                <td>
                                    <strong>{{ article.title|truncatechars:50 }}</strong>
                                    {% if article.is_featured %}
                                        <span class="badge bg-warning">Featured</span>
                                    {% endif %}
                                    {% if article.is_breaking %}
                                        <span class="badge bg-danger">Breaking</span>
                                    {% endif %}
                                </td>
                                <td>{{ article.category.name }}</td>
                                <td><strong>{{ article.views_count|floatformat:0 }}</strong></td>
                                <td>{{ article.timestamp|date:"M d, Y" }}</td>
                                <td>
                                    {% if article.is_published %}
                                        <span class="badge bg-success">Published</span>
                                    {% else %}
                                        <span class="badge bg-secondary">Draft</span>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Ad Performance -->
    <div class="col-lg-4 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-ad"></i> Ad Performance</h5>
            </div>
            <div class="card-body">
                {% for ad in ad_performance %}
                <div class="border-bottom pb-3 mb-3">
                    <div class="d-flex justify-content-between align-items-start">
                        <div>
                            <h6 class="mb-1">{{ ad.title|truncatechars:30 }}</h6>
                            <small class="text-muted">{{ ad.ad_type|title }} - {{ ad.ad_size }}</small>
                        </div>
                        <span class="badge bg-primary">{{ ad.clicks }} clicks</span>
                    </div>
                    <div class="mt-2">
                        <small class="text-muted">
                            {{ ad.impressions }} impressions | 
                            {% if ad.impressions > 0 %}
                                {{ ad.clicks|floatformat:0 }}/{{ ad.impressions|floatformat:0 }} = 
                                {% widthratio ad.clicks ad.impressions 100 %}% CTR
                            {% else %}
                                0% CTR
                            {% endif %}
                        </small>
                    </div>
                    <div class="progress mt-2" style="height: 4px;">
                        <div class="progress-bar" role="progressbar" 
                             style="width: {% if ad.impressions > 0 %}{% widthratio ad.clicks ad.impressions 100 %}{% else %}0{% endif %}%"></div>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
</div>

<!-- Real-time Stats -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-clock"></i> Real-time Activity</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 text-center">
                        <h4 class="text-primary" id="online-users">0</h4>
                        <small class="text-muted">Users Online</small>
                    </div>
                    <div class="col-md-3 text-center">
                        <h4 class="text-success" id="page-views-today">0</h4>
                        <small class="text-muted">Page Views Today</small>
                    </div>
                    <div class="col-md-3 text-center">
                        <h4 class="text-warning" id="new-subscribers">0</h4>
                        <small class="text-muted">New Subscribers</small>
                    </div>
                    <div class="col-md-3 text-center">
                        <h4 class="text-info" id="active-ads">0</h4>
                        <small class="text-muted">Active Ads</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Initialize charts and load data
document.addEventListener('DOMContentLoaded', function() {
    loadAnalyticsData();
    initializeCharts();
    
    // Auto-refresh every 30 seconds
    setInterval(loadAnalyticsData, 30000);
});

function loadAnalyticsData() {
    // Simulate loading analytics data
    document.getElementById('total-views').textContent = '{{ total_views|default:"0" }}';
    document.getElementById('total-ad-clicks').textContent = '{{ total_clicks|default:"0" }}';
    document.getElementById('total-impressions').textContent = '{{ total_impressions|default:"0" }}';
    document.getElementById('average-ctr').textContent = '{{ average_ctr|default:"0" }}%';
    
    // Real-time stats (simulated)
    document.getElementById('online-users').textContent = Math.floor(Math.random() * 50) + 10;
    document.getElementById('page-views-today').textContent = Math.floor(Math.random() * 1000) + 500;
    document.getElementById('new-subscribers').textContent = Math.floor(Math.random() * 20) + 5;
    document.getElementById('active-ads').textContent = '{{ active_ads_count|default:"0" }}';
}

function initializeCharts() {
    // News by Category Chart
    const categoryCtx = document.getElementById('categoryChart').getContext('2d');
    new Chart(categoryCtx, {
        type: 'doughnut',
        data: {
            labels: [{% for item in news_by_category %}'{{ item.name }}'{% if not forloop.last %},{% endif %}{% endfor %}],
            datasets: [{
                data: [{% for item in news_by_category %}{{ item.count }}{% if not forloop.last %},{% endif %}{% endfor %}],
                backgroundColor: [
                    '#1e3a8a', '#dc2626', '#059669', '#7c3aed', 
                    '#ea580c', '#0891b2', '#be185d', '#4338ca'
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });

    // Ad Performance Chart
    const adCtx = document.getElementById('adPerformanceChart').getContext('2d');
    new Chart(adCtx, {
        type: 'bar',
        data: {
            labels: [{% for ad in ad_performance %}'{{ ad.title|truncatechars:15 }}'{% if not forloop.last %},{% endif %}{% endfor %}],
            datasets: [{
                label: 'Clicks',
                data: [{% for ad in ad_performance %}{{ ad.clicks }}{% if not forloop.last %},{% endif %}{% endfor %}],
                backgroundColor: '#1e3a8a'
            }, {
                label: 'Impressions',
                data: [{% for ad in ad_performance %}{{ ad.impressions }}{% if not forloop.last %},{% endif %}{% endfor %}],
                backgroundColor: '#3b82f6'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
}
</script>
{% endblock %}
