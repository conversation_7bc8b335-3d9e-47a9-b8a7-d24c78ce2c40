#!/usr/bin/env python
"""
Quick test script for NewsData.io API integration
"""

import os
import django
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
django.setup()

def test_api_integration():
    print("🧪 Testing NewsData.io API Integration")
    print("=" * 50)
    
    # Test 1: Check environment variables
    print("1️⃣ Checking environment configuration...")
    api_key = os.environ.get('NEWSDATA_API_KEY', '')
    if api_key:
        print(f"   ✅ API Key configured: {api_key[:10]}...")
    else:
        print("   ❌ API Key not found in environment")
        return
    
    # Test 2: Import services
    print("2️⃣ Testing service imports...")
    try:
        from news.services import news_service
        print("   ✅ NewsData service imported successfully")
    except Exception as e:
        print(f"   ❌ Import error: {str(e)}")
        return
    
    # Test 3: Check API status
    print("3️⃣ Checking API status...")
    try:
        status = news_service.get_api_status()
        print(f"   ✅ API Key Configured: {status['api_key_configured']}")
        print(f"   ✅ Hourly Requests: {status['hourly_requests']}/{status['hourly_limit']}")
        print(f"   ✅ Daily Requests: {status['daily_requests']}/{status['daily_limit']}")
    except Exception as e:
        print(f"   ❌ Status check error: {str(e)}")
        return
    
    # Test 4: Test API call
    print("4️⃣ Testing API call...")
    try:
        articles = news_service.fetch_latest_news(category='technology', size=3)
        if articles:
            print(f"   ✅ Successfully fetched {len(articles)} articles")
            for i, article in enumerate(articles[:2], 1):
                print(f"      {i}. {article['title'][:60]}...")
        else:
            print("   ⚠️  No articles returned (might be rate limited or API issue)")
    except Exception as e:
        print(f"   ❌ API call error: {str(e)}")
        return
    
    # Test 5: Test database models
    print("5️⃣ Testing database models...")
    try:
        from news.models import News, Category, Author
        from django.contrib.auth.models import User
        
        # Check if we can create/get author
        user, created = User.objects.get_or_create(
            username='test_api_user',
            defaults={'email': '<EMAIL>'}
        )
        author, created = Author.objects.get_or_create(
            user=user
        )
        print(f"   ✅ Author ready: {author.user.username}")
        
        # Check categories
        category_count = Category.objects.count()
        print(f"   ✅ Categories in database: {category_count}")
        
        # Check news count
        news_count = News.objects.count()
        print(f"   ✅ News articles in database: {news_count}")
        
    except Exception as e:
        print(f"   ❌ Database error: {str(e)}")
        return
    
    print("\n🎉 All tests passed! API integration is ready.")
    print("\n📋 Next steps:")
    print("   1. Run: python manage.py fetch_news --category=technology --size=5")
    print("   2. Visit: http://127.0.0.1:8000/custom-admin/news-api/")
    print("   3. Check: http://127.0.0.1:8000/api/newsdata/status/")

if __name__ == "__main__":
    test_api_integration()
