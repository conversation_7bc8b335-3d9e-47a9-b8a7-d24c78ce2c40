#!/usr/bin/env python
"""
Simple backup script for the news portal database
"""

import os
import django
from datetime import datetime
import shutil

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
django.setup()

def create_backup():
    """Create a simple database backup"""
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    
    print(f"🗄️ Creating database backup - {timestamp}")
    
    # Create backups directory
    backup_dir = 'backups'
    os.makedirs(backup_dir, exist_ok=True)
    
    # For SQLite, just copy the database file
    from django.conf import settings
    db_path = settings.DATABASES['default']['NAME']
    
    if os.path.exists(db_path):
        backup_filename = f'news_portal_db_backup_{timestamp}.db'
        backup_path = os.path.join(backup_dir, backup_filename)
        
        # Copy database file
        shutil.copy2(db_path, backup_path)
        
        # Get file size
        size = os.path.getsize(backup_path)
        size_mb = size / (1024 * 1024)
        
        print(f"✅ Database backup created: {backup_filename}")
        print(f"📊 Backup size: {size_mb:.2f} MB")
        print(f"📁 Location: {backup_path}")
        
        # Also create a JSON dump
        json_backup = f'news_portal_data_backup_{timestamp}.json'
        json_path = os.path.join(backup_dir, json_backup)
        
        os.system(f'python manage.py dumpdata --indent=2 > "{json_path}"')
        
        if os.path.exists(json_path):
            json_size = os.path.getsize(json_path)
            json_size_mb = json_size / (1024 * 1024)
            print(f"✅ JSON backup created: {json_backup}")
            print(f"📊 JSON size: {json_size_mb:.2f} MB")
        
        return True
    else:
        print(f"❌ Database file not found: {db_path}")
        return False

def get_database_stats():
    """Get current database statistics"""
    from news.models import News, Category, Author
    from django.contrib.auth.models import User
    
    print("\n📊 Current Database Statistics:")
    print("=" * 40)
    print(f"📰 Total News Articles: {News.objects.count()}")
    print(f"📂 Categories: {Category.objects.count()}")
    print(f"👥 Authors: <AUTHORS>
    print(f"🔐 Users: {User.objects.count()}")
    
    # Published vs unpublished
    published = News.objects.filter(is_published=True).count()
    unpublished = News.objects.filter(is_published=False).count()
    print(f"✅ Published Articles: {published}")
    print(f"📝 Draft Articles: {unpublished}")
    
    # Breaking news
    breaking = News.objects.filter(is_breaking=True).count()
    featured = News.objects.filter(is_featured=True).count()
    trending = News.objects.filter(is_trending=True).count()
    
    print(f"🚨 Breaking News: {breaking}")
    print(f"⭐ Featured Articles: {featured}")
    print(f"🔥 Trending Articles: {trending}")
    
    # API sourced news
    try:
        api_news = News.objects.filter(author__user__username='newsdata_api').count()
        print(f"📡 API Sourced News: {api_news}")
    except:
        print(f"📡 API Sourced News: 0")
    
    # Categories breakdown
    print(f"\n📂 News by Category:")
    for category in Category.objects.all():
        count = News.objects.filter(category=category, is_published=True).count()
        print(f"   {category.name}: {count} articles")

if __name__ == "__main__":
    print("🚀 News Portal Database Backup Tool")
    print("=" * 50)
    
    # Show current stats
    get_database_stats()
    
    # Create backup
    print("\n" + "=" * 50)
    success = create_backup()
    
    if success:
        print("\n🎉 Backup completed successfully!")
        print("\n💡 To restore from backup:")
        print("   1. Copy the .db file back to replace db.sqlite3")
        print("   2. Or use: python manage.py loaddata backup_file.json")
    else:
        print("\n❌ Backup failed!")
