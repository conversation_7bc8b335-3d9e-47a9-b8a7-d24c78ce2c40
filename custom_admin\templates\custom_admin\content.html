{% extends 'custom_admin/base.html' %}

{% block title %}Content Management{% endblock %}
{% block page_title %}Content Management{% endblock %}

{% block content %}
<!-- Quick Actions -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Quick Actions</h5>
                    <div>
                        <a href="/admin/news/news/add/" class="btn btn-modern me-2">
                            <i class="fas fa-plus"></i> Add New Article
                        </a>
                        <a href="/admin/news/category/add/" class="btn btn-outline-primary me-2">
                            <i class="fas fa-folder-plus"></i> Add Category
                        </a>
                        <button class="btn btn-outline-success" onclick="bulkPublish()">
                            <i class="fas fa-check-circle"></i> Bulk Publish
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Content Statistics -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stat-card">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">Total Articles</div>
                        <div class="h5 mb-0 font-weight-bold">{{ all_news.count }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-newspaper fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stat-card-success">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">Published</div>
                        <div class="h5 mb-0 font-weight-bold">{{ all_news|length }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-check-circle fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stat-card-warning">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">Drafts</div>
                        <div class="h5 mb-0 font-weight-bold">0</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-edit fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stat-card-info">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">Categories</div>
                        <div class="h5 mb-0 font-weight-bold">{{ categories.count }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-folder fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Content Filters -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-md-3">
                        <select class="form-select" id="categoryFilter">
                            <option value="">All Categories</option>
                            {% for category in categories %}
                            <option value="{{ category.id }}">{{ category.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-3">
                        <select class="form-select" id="statusFilter">
                            <option value="">All Status</option>
                            <option value="published">Published</option>
                            <option value="draft">Draft</option>
                            <option value="featured">Featured</option>
                            <option value="breaking">Breaking</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <input type="text" class="form-control" id="searchInput" placeholder="Search articles...">
                    </div>
                    <div class="col-md-3">
                        <button class="btn btn-primary" onclick="applyFilters()">
                            <i class="fas fa-filter"></i> Apply Filters
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Content Table -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-list"></i> All Articles</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover" id="contentTable">
                        <thead>
                            <tr>
                                <th>
                                    <input type="checkbox" id="selectAll" onchange="toggleSelectAll()">
                                </th>
                                <th>Title</th>
                                <th>Author</th>
                                <th>Category</th>
                                <th>Status</th>
                                <th>Views</th>
                                <th>Date</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for article in all_news %}
                            <tr data-article-id="{{ article.id }}">
                                <td>
                                    <input type="checkbox" class="article-checkbox" value="{{ article.id }}">
                                </td>
                                <td>
                                    <div>
                                        <strong>{{ article.title|truncatechars:50 }}</strong>
                                        <div class="mt-1">
                                            {% if article.is_featured %}
                                                <span class="badge bg-warning">Featured</span>
                                            {% endif %}
                                            {% if article.is_breaking %}
                                                <span class="badge bg-danger">Breaking</span>
                                            {% endif %}
                                            {% if article.is_trending %}
                                                <span class="badge bg-info">Trending</span>
                                            {% endif %}
                                            <span class="badge bg-secondary">{{ article.priority|title }}</span>
                                        </div>
                                    </div>
                                </td>
                                <td>{{ article.author.user.get_full_name|default:article.author.user.username }}</td>
                                <td>
                                    <span class="category-badge">{{ article.category.name }}</span>
                                </td>
                                <td>
                                    {% if article.is_published %}
                                        <span class="badge bg-success">Published</span>
                                    {% else %}
                                        <span class="badge bg-secondary">Draft</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <strong>{{ article.views_count }}</strong>
                                    <small class="text-muted d-block">{{ article.reading_time }} min read</small>
                                </td>
                                <td>
                                    <div>{{ article.timestamp|date:"M d, Y" }}</div>
                                    <small class="text-muted">{{ article.timestamp|time:"H:i" }}</small>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="/admin/news/news/{{ article.id }}/change/" class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="{{ article.get_absolute_url }}" target="_blank" class="btn btn-sm btn-outline-info">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <button class="btn btn-sm btn-outline-success" onclick="togglePublish({{ article.id }}, {{ article.is_published|yesno:'true,false' }})">
                                            {% if article.is_published %}
                                                <i class="fas fa-eye-slash"></i>
                                            {% else %}
                                                <i class="fas fa-check"></i>
                                            {% endif %}
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Bulk Actions Modal -->
<div class="modal fade" id="bulkActionsModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Bulk Actions</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Select an action to apply to <span id="selectedCount">0</span> selected articles:</p>
                <div class="d-grid gap-2">
                    <button class="btn btn-success" onclick="bulkAction('publish')">
                        <i class="fas fa-check-circle"></i> Publish Selected
                    </button>
                    <button class="btn btn-secondary" onclick="bulkAction('unpublish')">
                        <i class="fas fa-eye-slash"></i> Unpublish Selected
                    </button>
                    <button class="btn btn-warning" onclick="bulkAction('feature')">
                        <i class="fas fa-star"></i> Mark as Featured
                    </button>
                    <button class="btn btn-danger" onclick="bulkAction('unfeature')">
                        <i class="fas fa-star-o"></i> Remove Featured
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function toggleSelectAll() {
    const selectAll = document.getElementById('selectAll');
    const checkboxes = document.querySelectorAll('.article-checkbox');
    
    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAll.checked;
    });
    
    updateBulkActionsButton();
}

function updateBulkActionsButton() {
    const selectedCheckboxes = document.querySelectorAll('.article-checkbox:checked');
    const count = selectedCheckboxes.length;
    
    if (count > 0) {
        document.getElementById('selectedCount').textContent = count;
    }
}

function togglePublish(articleId, isPublished) {
    fetch(`{% url 'custom_admin:toggle_news' 0 %}`.replace('0', articleId), {
        method: 'POST',
        headers: {
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
            'Content-Type': 'application/json',
        },
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('Error updating article status');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error updating article status');
    });
}

function bulkPublish() {
    const selectedCheckboxes = document.querySelectorAll('.article-checkbox:checked');
    if (selectedCheckboxes.length === 0) {
        alert('Please select articles to publish');
        return;
    }
    
    const modal = new bootstrap.Modal(document.getElementById('bulkActionsModal'));
    modal.show();
}

function bulkAction(action) {
    const selectedCheckboxes = document.querySelectorAll('.article-checkbox:checked');
    const articleIds = Array.from(selectedCheckboxes).map(cb => cb.value);
    
    if (articleIds.length === 0) {
        alert('No articles selected');
        return;
    }
    
    // Here you would implement the bulk action API call
    console.log(`Performing ${action} on articles:`, articleIds);
    
    // Close modal and reload page
    bootstrap.Modal.getInstance(document.getElementById('bulkActionsModal')).hide();
    setTimeout(() => location.reload(), 500);
}

function applyFilters() {
    const category = document.getElementById('categoryFilter').value;
    const status = document.getElementById('statusFilter').value;
    const search = document.getElementById('searchInput').value;
    
    // Here you would implement filtering logic
    console.log('Applying filters:', { category, status, search });
}

// Add event listeners for checkboxes
document.addEventListener('DOMContentLoaded', function() {
    const checkboxes = document.querySelectorAll('.article-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.addEventListener('change', updateBulkActionsButton);
    });
});
</script>
{% endblock %}
