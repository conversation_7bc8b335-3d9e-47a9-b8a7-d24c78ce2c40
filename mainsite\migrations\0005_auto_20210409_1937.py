# Generated by Django 3.1.4 on 2021-04-09 13:37

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('news', '0008_news_author'),
        ('mainsite', '0004_auto_20210107_2315'),
    ]

    operations = [
        migrations.AlterField(
            model_name='homepagesettings',
            name='hot_news',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='hot_news', to='news.news'),
        ),
        migrations.AlterField(
            model_name='homepagesettings',
            name='post_catalog_five',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='post_catalog_five', to='news.category'),
        ),
        migrations.AlterField(
            model_name='homepagesettings',
            name='post_catalog_four',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='post_catalog_four', to='news.category'),
        ),
        migrations.Alter<PERSON>ield(
            model_name='homepagesettings',
            name='post_catalog_one',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='post_catalog_one', to='news.category'),
        ),
        migrations.AlterField(
            model_name='homepagesettings',
            name='post_catalog_three',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='post_catalog_three', to='news.category'),
        ),
        migrations.AlterField(
            model_name='homepagesettings',
            name='post_catalog_two',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='post_catalog_two', to='news.category'),
        ),
    ]
