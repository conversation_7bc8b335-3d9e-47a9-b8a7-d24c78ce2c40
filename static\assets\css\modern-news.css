/* Modern News Portal CSS - NBC News Style */

:root {
    /* NBC News Color Palette */
    --nbc-blue: #0078d4;
    --nbc-dark-blue: #004578;
    --nbc-red: #d13438;
    --nbc-gold: #ffb900;
    --nbc-purple: #5c2d91;

    /* Primary Colors */
    --primary-color: #0078d4;
    --primary-dark: #004578;
    --secondary-color: #d13438;
    --accent-color: #ffb900;

    /* Text Colors */
    --text-primary: #1a1a1a;
    --text-secondary: #4a4a4a;
    --text-muted: #6a6a6a;
    --text-light: #8a8a8a;

    /* Background Colors */
    --bg-primary: #ffffff;
    --bg-secondary: #f8f9fa;
    --bg-tertiary: #e9ecef;
    --bg-dark: #1a1a1a;

    /* Border & Shadow */
    --border-light: #e1e5e9;
    --border-medium: #d1d5db;
    --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1);
    --shadow-md: 0 4px 12px rgba(0, 0, 0, 0.15);
    --shadow-lg: 0 8px 25px rgba(0, 0, 0, 0.2);
    --shadow-xl: 0 20px 40px rgba(0, 0, 0, 0.25);

    /* Gradients */
    --gradient-primary: linear-gradient(135deg, #0078d4 0%, #106ebe 100%);
    --gradient-secondary: linear-gradient(135deg, #d13438 0%, #b02a2e 100%);
    --gradient-accent: linear-gradient(135deg, #ffb900 0%, #e6a600 100%);

    /* Typography */
    --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    --font-secondary: 'Playfair Display', Georgia, serif;
    --font-mono: 'JetBrains Mono', 'Fira Code', Consolas, monospace;

    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;

    /* Border Radius */
    --radius-sm: 4px;
    --radius-md: 8px;
    --radius-lg: 12px;
    --radius-xl: 16px;
    --radius-full: 9999px;

    /* Transitions */
    --transition-fast: 0.15s ease-in-out;
    --transition-normal: 0.3s ease-in-out;
    --transition-slow: 0.5s ease-in-out;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-primary);
    font-size: 16px;
    line-height: 1.6;
    color: var(--text-primary);
    background-color: var(--bg-primary);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Typography Improvements */
h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-primary);
    font-weight: 700;
    line-height: 1.2;
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
}

h1 { font-size: 2.5rem; font-weight: 800; }
h2 { font-size: 2rem; font-weight: 700; }
h3 { font-size: 1.75rem; font-weight: 600; }
h4 { font-size: 1.5rem; font-weight: 600; }

/* ===== NBC NEWS STYLE LAYOUT ===== */

/* Top Banner Ad */
.top-banner-ad {
    background-color: var(--bg-secondary);
    border-bottom: 1px solid var(--border-light);
}

.horizontal-ad-banner {
    max-width: 100%;
    margin: 0 auto;
}

.horizontal-ad-banner.large {
    max-width: 970px;
}

.ad-content {
    position: relative;
}

.ad-label {
    font-size: 0.75rem;
    color: var(--text-muted);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    display: block;
    margin-bottom: 0.5rem;
}

.ad-placeholder {
    border-radius: var(--radius-md);
    overflow: hidden;
}

/* NBC Main Layout */
.nbc-main-layout {
    background-color: var(--bg-primary);
}

/* Main Story Card */
.main-story-card {
    background: var(--bg-primary);
    border-radius: var(--radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-md);
    transition: var(--transition-normal);
}

.main-story-card:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-2px);
}

.story-image-container {
    position: relative;
    overflow: hidden;
}

.main-story-image {
    width: 100%;
    height: 400px;
    object-fit: cover;
    transition: var(--transition-normal);
}

.main-story-card:hover .main-story-image {
    transform: scale(1.05);
}

.breaking-overlay {
    position: absolute;
    top: 1rem;
    left: 1rem;
}

.breaking-badge {
    background: var(--secondary-color);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: var(--radius-full);
    font-size: 0.875rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    display: flex;
    align-items: center;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.8; }
}

.story-content {
    padding: 2rem;
}

.main-story-title {
    font-size: 2rem;
    font-weight: 800;
    line-height: 1.2;
    margin-bottom: 1rem;
}

.main-story-title a {
    color: var(--text-primary);
    text-decoration: none;
    transition: var(--transition-fast);
}

.main-story-title a:hover {
    color: var(--primary-color);
}

.main-story-excerpt {
    font-size: 1.125rem;
    line-height: 1.6;
    color: var(--text-secondary);
    margin-bottom: 1.5rem;
}

.story-meta {
    display: flex;
    gap: 1.5rem;
    flex-wrap: wrap;
}

.meta-item {
    display: flex;
    align-items: center;
    font-size: 0.875rem;
    color: var(--text-muted);
}

.meta-item i {
    color: var(--primary-color);
}

/* Secondary Stories */
.secondary-stories {
    margin-top: 2rem;
}

.section-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 1.5rem;
    padding-bottom: 0.5rem;
    border-bottom: 3px solid var(--primary-color);
    display: flex;
    align-items: center;
    line-height: 1.2;
}

.section-title i {
    margin-right: 0.5rem;
    font-size: 1.2rem;
    color: var(--primary-color);
    vertical-align: middle;
    flex-shrink: 0;
}

.secondary-story-card {
    background: var(--bg-primary);
    border-radius: var(--radius-md);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
    transition: var(--transition-normal);
    height: 100%;
}

.secondary-story-card:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-1px);
}

.secondary-story-image {
    width: 100%;
    height: 150px;
    object-fit: cover;
}

.secondary-story-content {
    padding: 1rem;
}

.secondary-story-title {
    font-size: 1rem;
    font-weight: 600;
    line-height: 1.3;
    margin-bottom: 0.5rem;
}

.secondary-story-title a {
    color: var(--text-primary);
    text-decoration: none;
    transition: var(--transition-fast);
}

.secondary-story-title a:hover {
    color: var(--primary-color);
}

.secondary-story-meta {
    font-size: 0.875rem;
    color: var(--text-muted);
}

/* Sub News Section (Middle Column) */
.sub-news-section {
    background: var(--bg-primary);
    border-radius: var(--radius-lg);
    padding: 1.5rem;
    box-shadow: var(--shadow-sm);
    height: fit-content;
}

.sub-news-item {
    display: flex;
    gap: 1rem;
    padding: 1rem 0;
    border-bottom: 1px solid var(--border-light);
}

.sub-news-item:last-child {
    border-bottom: none;
}

.sub-news-image {
    flex-shrink: 0;
    width: 80px;
    height: 60px;
    border-radius: var(--radius-sm);
    overflow: hidden;
}

.sub-news-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.sub-news-content {
    flex: 1;
}

.sub-news-title {
    font-size: 0.875rem;
    font-weight: 600;
    line-height: 1.3;
    margin-bottom: 0.25rem;
}

.sub-news-title a {
    color: var(--text-primary);
    text-decoration: none;
    transition: var(--transition-fast);
}

.sub-news-title a:hover {
    color: var(--primary-color);
}

.sub-news-meta {
    font-size: 0.75rem;
    color: var(--text-muted);
}

/* Live Section (Right Column) */
.live-section {
    background: var(--bg-primary);
    border-radius: var(--radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
}

.live-header {
    background: linear-gradient(135deg, var(--secondary-color), #b02a2e);
    color: white;
    padding: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.live-indicator {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 700;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.live-dot {
    width: 8px;
    height: 8px;
    background: white;
    border-radius: 50%;
    animation: livePulse 1.5s infinite;
}

@keyframes livePulse {
    0%, 100% { opacity: 1; transform: scale(1); }
    50% { opacity: 0.7; transform: scale(1.2); }
}

.live-source {
    font-size: 0.875rem;
    opacity: 0.9;
}

.live-content {
    padding: 1.5rem;
}

.live-image {
    width: 100%;
    height: 150px;
    object-fit: cover;
    border-radius: var(--radius-md);
    margin-bottom: 1rem;
}

.live-title {
    font-size: 1rem;
    font-weight: 600;
    line-height: 1.3;
}

.live-title a {
    color: var(--text-primary);
    text-decoration: none;
    transition: var(--transition-fast);
}

.live-title a:hover {
    color: var(--primary-color);
}

/* Latest News Section */
.latest-news-section {
    background: var(--bg-primary);
    border-radius: var(--radius-lg);
    padding: 1.5rem;
    box-shadow: var(--shadow-sm);
}

.latest-news-item {
    padding: 1rem 0;
    border-bottom: 1px solid var(--border-light);
}

.latest-news-item:last-child {
    border-bottom: none;
}

.news-time {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
}

.time-badge {
    background: var(--primary-color);
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: var(--radius-sm);
    font-size: 0.75rem;
    font-weight: 600;
}

.time-ago {
    font-size: 0.75rem;
    color: var(--text-muted);
}

.latest-news-title {
    font-size: 0.875rem;
    font-weight: 600;
    line-height: 1.3;
}

.latest-news-title a {
    color: var(--text-primary);
    text-decoration: none;
    transition: var(--transition-fast);
}

.latest-news-title a:hover {
    color: var(--primary-color);
}

/* Enhanced News Hierarchy Styles */
.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.live-indicator {
    display: flex;
    align-items: center;
    background: var(--secondary-color);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    animation: pulse 2s infinite;
}

.live-dot {
    width: 8px;
    height: 8px;
    background: white;
    border-radius: 50%;
    margin-right: 0.5rem;
    animation: blink 1s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

@keyframes blink {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.3; }
}

/* Breaking News Alert */
.breaking-alert {
    background: linear-gradient(135deg, #dc3545, #c82333);
    color: white;
    border-radius: var(--radius-md);
    padding: 1rem;
    margin-bottom: 1rem;
    animation: slideInDown 0.5s ease-out;
}

.breaking-header {
    display: flex;
    align-items: center;
    font-weight: 700;
    font-size: 0.875rem;
    margin-bottom: 0.5rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.breaking-header i {
    margin-right: 0.5rem;
    animation: flash 1s infinite;
}

@keyframes flash {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

.breaking-item {
    padding: 0.5rem 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.breaking-item:last-child {
    border-bottom: none;
}

.breaking-link {
    color: white;
    text-decoration: none;
    font-weight: 600;
    display: block;
    margin-bottom: 0.25rem;
}

.breaking-link:hover {
    color: #ffeb3b;
    text-decoration: underline;
}

.breaking-time {
    font-size: 0.75rem;
    opacity: 0.8;
}

/* News Stack */
.news-stack {
    margin-bottom: 2rem;
}

.stack-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid var(--border-light);
}

.stack-title {
    font-size: 1rem;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0;
    display: flex;
    align-items: center;
}

.stack-title i {
    margin-right: 0.5rem;
    color: var(--primary-color);
}

.update-count {
    background: var(--primary-color);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.75rem;
    font-weight: 600;
}

/* Priority Badges */
.priority-badge {
    display: inline-block;
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-size: 0.7rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.priority-badge.high {
    background: #dc3545;
    color: white;
}

.priority-badge.trending {
    background: #ff6b35;
    color: white;
}

.priority-badge.featured {
    background: #28a745;
    color: white;
}

/* Enhanced Latest News Items */
.latest-news-item.priority-high {
    border-left: 4px solid #dc3545;
    background: rgba(220, 53, 69, 0.05);
}

.latest-news-item.trending {
    border-left: 4px solid #ff6b35;
    background: rgba(255, 107, 53, 0.05);
}

.news-priority {
    margin-right: 0.75rem;
    min-width: 60px;
}

.news-content {
    flex: 1;
}

.news-meta {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 0.5rem;
    font-size: 0.75rem;
}

.news-category {
    background: var(--primary-color);
    color: white;
    padding: 0.125rem 0.5rem;
    border-radius: 10px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.news-engagement {
    display: flex;
    gap: 1rem;
    font-size: 0.75rem;
    color: var(--text-muted);
}

.engagement-item {
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

/* Trending Stack */
.trending-stack {
    background: linear-gradient(135deg, #ff6b35, #f7931e);
    color: white;
    border-radius: var(--radius-md);
    padding: 1rem;
}

.trending-item {
    display: flex;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.trending-item:last-child {
    border-bottom: none;
}

.trending-rank {
    width: 30px;
    height: 30px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    margin-right: 1rem;
    flex-shrink: 0;
}

.trending-content {
    flex: 1;
}

.trending-title {
    font-size: 0.875rem;
    font-weight: 600;
    line-height: 1.3;
    margin-bottom: 0.25rem;
}

.trending-title a {
    color: white;
    text-decoration: none;
}

.trending-title a:hover {
    color: #ffeb3b;
    text-decoration: underline;
}

.trending-stats {
    display: flex;
    gap: 1rem;
    font-size: 0.75rem;
    opacity: 0.9;
}

/* Priority Stories in Sub-News */
.sub-news-item.priority-story {
    position: relative;
    border-left: 3px solid var(--primary-color);
    background: rgba(0, 120, 212, 0.05);
}

.story-priority-indicator {
    position: absolute;
    top: 0.5rem;
    left: -1px;
}

.priority-dot {
    width: 8px;
    height: 8px;
    background: var(--primary-color);
    border-radius: 50%;
    animation: pulse 2s infinite;
}

.featured-overlay {
    position: absolute;
    top: 0.5rem;
    right: 0.5rem;
}

.featured-badge {
    background: var(--nbc-gold);
    color: var(--text-primary);
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-size: 0.7rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.story-category {
    font-size: 0.7rem;
    color: var(--primary-color);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 0.25rem;
}

.meta-views {
    color: var(--text-muted);
    font-size: 0.75rem;
}

/* Mini Trending Stories */
.trending-stories-mini {
    background: var(--bg-secondary);
    border-radius: var(--radius-md);
    padding: 1rem;
    border: 1px solid var(--border-light);
}

.mini-section-title {
    font-size: 0.875rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 0.75rem;
    display: flex;
    align-items: center;
}

.mini-section-title i {
    margin-right: 0.5rem;
    color: #ff6b35;
}

.mini-story-item {
    display: flex;
    align-items: flex-start;
    padding: 0.5rem 0;
    border-bottom: 1px solid var(--border-light);
}

.mini-story-item:last-child {
    border-bottom: none;
}

.mini-story-rank {
    width: 20px;
    height: 20px;
    background: var(--primary-color);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.75rem;
    font-weight: 700;
    margin-right: 0.75rem;
    flex-shrink: 0;
}

.mini-story-content {
    flex: 1;
}

.mini-story-title {
    font-size: 0.8rem;
    font-weight: 600;
    color: var(--text-primary);
    text-decoration: none;
    line-height: 1.3;
    display: block;
    margin-bottom: 0.25rem;
}

.mini-story-title:hover {
    color: var(--primary-color);
}

.mini-story-meta {
    font-size: 0.7rem;
    color: var(--text-muted);
}

/* Advertisement Sections */
.middle-ad-section,
.sidebar-ad-section {
    background: var(--bg-primary);
    border-radius: var(--radius-lg);
    padding: 1rem;
    box-shadow: var(--shadow-sm);
}

.horizontal-banner-ad {
    background: var(--bg-secondary);
    border-radius: var(--radius-lg);
    padding: 1.5rem;
    margin: 0 auto;
}

.bottom-banner-ad {
    background: var(--bg-secondary);
    border-top: 1px solid var(--border-light);
}

/* Dynamic Ad Styles */
.ad-container {
    margin: 0 auto;
    text-align: center;
}

/* Hide ad sections when no ads are available */
.middle-ad-section:empty,
.sidebar-ad-section:empty,
.horizontal-banner-ad:empty,
.bottom-banner-ad:empty {
    display: none !important;
    margin: 0 !important;
    padding: 0 !important;
}

/* Hide parent sections when they contain only empty ad containers */
.top-banner-ad:has(.ad-container:empty),
.horizontal-banner-ad:has(.ad-container:empty),
.bottom-banner-ad:has(.ad-container:empty) {
    display: none !important;
}

.ad-content {
    position: relative;
}

.ad-label {
    font-size: 0.75rem;
    color: var(--text-muted);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    display: block;
    margin-bottom: 0.5rem;
    opacity: 0.7;
}

.ad-image-content,
.ad-html-content,
.ad-text-content {
    border-radius: var(--radius-md);
    overflow: hidden;
    transition: var(--transition-normal);
}

.ad-image-content:hover,
.ad-html-content:hover,
.ad-text-content:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.ad-image {
    width: 100%;
    height: auto;
    display: block;
    transition: var(--transition-normal);
}

.ad-image:hover {
    transform: scale(1.02);
}

.ad-text-content {
    background: var(--bg-primary);
    padding: 2rem;
    border: 1px solid var(--border-light);
}

.ad-text-content h4 {
    color: var(--primary-color);
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
}

.ad-placeholder {
    opacity: 0.8;
}

.ad-fallback {
    border-radius: var(--radius-md);
    overflow: hidden;
}

/* Ad Container Specific Styles */
.header-ad .ad-container {
    max-width: 728px;
}

.billboard-ad .ad-container {
    max-width: 970px;
}

.middle-ad .ad-container,
.right-sidebar-ad .ad-container,
.category-sidebar-ad .ad-container {
    max-width: 300px;
}

.footer-ad .ad-container {
    max-width: 728px;
}

/* Responsive Ad Adjustments */
@media (max-width: 1200px) {
    .billboard-ad .ad-container {
        max-width: 728px;
    }
}

@media (max-width: 768px) {
    .header-ad .ad-container,
    .footer-ad .ad-container {
        max-width: 320px;
    }

    .middle-ad .ad-container,
    .right-sidebar-ad .ad-container,
    .category-sidebar-ad .ad-container {
        max-width: 100%;
    }

    .billboard-ad .ad-container {
        max-width: 320px;
    }

    .ad-label {
        font-size: 0.7rem;
    }
}

/* Category News Sections */
.category-news-sections {
    background: var(--bg-secondary);
}

.category-section {
    background: var(--bg-primary);
    border-radius: var(--radius-lg);
    padding: 2rem;
    box-shadow: var(--shadow-sm);
}

.category-header {
    margin-bottom: 2rem;
}

.category-title {
    font-size: 1.75rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.category-title a {
    color: var(--text-primary);
    text-decoration: none;
    transition: var(--transition-fast);
}

.category-title a:hover {
    color: var(--primary-color);
}

.category-line {
    height: 4px;
    width: 60px;
    background: var(--primary-color);
    border-radius: var(--radius-full);
}

.category-main-story {
    margin-bottom: 2rem;
}

.category-image {
    border-radius: var(--radius-md);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
}

.category-image img {
    width: 100%;
    height: 200px;
    object-fit: cover;
    transition: var(--transition-normal);
}

.category-image:hover img {
    transform: scale(1.05);
}

.category-content {
    padding-left: 1rem;
}

.tag-badge {
    background: var(--primary-color);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: var(--radius-full);
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-right: 0.5rem;
    margin-bottom: 0.75rem;
    display: inline-block;
}

.tag-badge.tech {
    background: var(--nbc-purple);
}

.category-story-title {
    font-size: 1.5rem;
    font-weight: 700;
    line-height: 1.3;
    margin-bottom: 1rem;
}

.category-story-title a {
    color: var(--text-primary);
    text-decoration: none;
    transition: var(--transition-fast);
}

.category-story-title a:hover {
    color: var(--primary-color);
}

.story-meta {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
    font-size: 0.875rem;
}

.meta-date,
.meta-author {
    color: var(--text-muted);
}

.meta-author {
    font-weight: 600;
}

.story-excerpt {
    font-size: 1rem;
    line-height: 1.6;
    color: var(--text-secondary);
    margin-bottom: 1rem;
}

.story-engagement {
    display: flex;
    gap: 1.5rem;
}

.engagement-item {
    display: flex;
    align-items: center;
    font-size: 0.875rem;
    color: var(--text-muted);
}

.engagement-item i {
    color: var(--primary-color);
}

.category-more {
    text-align: center;
    padding-top: 1.5rem;
    border-top: 1px solid var(--border-light);
}

.more-link {
    color: var(--primary-color);
    font-weight: 600;
    text-decoration: none;
    transition: var(--transition-fast);
}

.more-link:hover {
    color: var(--primary-dark);
    text-decoration: underline;
}

.more-link.tech {
    color: var(--nbc-purple);
}

.more-link.tech:hover {
    color: #4a1f73;
}

/* Category Sidebar */
.category-sidebar {
    padding-left: 1.5rem;
}

.sidebar-title {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid var(--primary-color);
}

.related-stories {
    margin-bottom: 2rem;
}

.related-story-item {
    padding: 0.75rem 0;
    border-bottom: 1px solid var(--border-light);
}

.related-story-item:last-child {
    border-bottom: none;
}

.related-story-title {
    font-size: 0.875rem;
    font-weight: 600;
    line-height: 1.3;
    margin-bottom: 0.25rem;
}

.related-story-title a {
    color: var(--text-primary);
    text-decoration: none;
    transition: var(--transition-fast);
}

.related-story-title a:hover {
    color: var(--primary-color);
}

/* Technology Section Specific Styles */
.technology-section {
    background: var(--bg-secondary);
}

.tech-story-grid {
    margin-bottom: 2rem;
}

.tech-story-card {
    background: var(--bg-primary);
    border-radius: var(--radius-lg);
    padding: 1.5rem;
    box-shadow: var(--shadow-sm);
    transition: var(--transition-normal);
    height: 100%;
}

.tech-story-card:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.tech-image {
    border-radius: var(--radius-md);
    overflow: hidden;
    margin-bottom: 1rem;
}

.tech-image img {
    width: 100%;
    height: 200px;
    object-fit: cover;
    transition: var(--transition-normal);
}

.tech-story-card:hover .tech-image img {
    transform: scale(1.05);
}

.tech-story-title {
    font-size: 1.25rem;
    font-weight: 700;
    line-height: 1.3;
    margin-bottom: 1rem;
}

.tech-story-title a {
    color: var(--text-primary);
    text-decoration: none;
    transition: var(--transition-fast);
}

.tech-story-title a:hover {
    color: var(--nbc-purple);
}

.tech-sidebar {
    padding-left: 1.5rem;
}

.trending-tech {
    background: var(--bg-primary);
    border-radius: var(--radius-lg);
    padding: 1.5rem;
    box-shadow: var(--shadow-sm);
}

.trending-tech-item {
    padding: 0.75rem 0;
    border-bottom: 1px solid var(--border-light);
}

.trending-tech-item:last-child {
    border-bottom: none;
}

.trending-tech-title {
    font-size: 0.875rem;
    font-weight: 600;
    line-height: 1.3;
    margin-bottom: 0.25rem;
}

.trending-tech-title a {
    color: var(--text-primary);
    text-decoration: none;
    transition: var(--transition-fast);
}

.trending-tech-title a:hover {
    color: var(--nbc-purple);
}

/* Enhanced Responsive Design */

/* Large Desktop (1200px and up) */
@media (max-width: 1400px) {
    .container-fluid {
        padding-left: 2rem;
        padding-right: 2rem;
    }
}

/* Desktop (992px to 1199px) */
@media (max-width: 1200px) {
    .horizontal-ad-banner.large {
        max-width: 728px;
    }

    .horizontal-ad-banner.large .ad-placeholder > div {
        height: 90px !important;
    }

    .container-fluid {
        padding-left: 1.5rem;
        padding-right: 1.5rem;
    }

    .navbar-modern .container-fluid {
        padding: 0.5rem 1.5rem;
    }
}

/* Tablet (768px to 991px) */
@media (max-width: 992px) {
    .main-story-image {
        height: 300px;
    }

    .main-story-title {
        font-size: 1.75rem;
        line-height: 1.3;
    }

    .story-content {
        padding: 1.5rem;
    }

    .category-sidebar,
    .tech-sidebar {
        padding-left: 0;
        margin-top: 2rem;
    }

    .sub-news-section,
    .live-section,
    .latest-news-section {
        margin-bottom: 2rem;
    }

    /* Navbar improvements for tablet */
    .navbar-nav .nav-link {
        padding: 0.75rem 1rem;
        font-size: 0.95rem;
    }

    .brand-text .brand-main {
        font-size: 1.5rem;
    }

    .brand-text .brand-sub {
        font-size: 0.75rem;
    }
}

/* Mobile Large (576px to 767px) */
@media (max-width: 768px) {
    .nbc-main-layout {
        padding: 1rem 0;
    }

    .container-fluid {
        padding-left: 1rem;
        padding-right: 1rem;
    }

    .main-story-image {
        height: 250px;
    }

    .main-story-title {
        font-size: 1.5rem;
        line-height: 1.4;
    }

    .main-story-excerpt {
        font-size: 1rem;
        line-height: 1.5;
    }

    .story-content {
        padding: 1rem;
    }

    .story-meta {
        flex-direction: column;
        gap: 0.5rem;
    }

    .secondary-story-image {
        height: 120px;
    }

    .category-section {
        padding: 1.5rem;
    }

    .category-content {
        padding-left: 0;
        margin-top: 1rem;
    }

    .horizontal-ad-banner {
        padding: 1rem;
    }

    .horizontal-ad-banner .ad-placeholder > div {
        height: 60px !important;
        font-size: 0.875rem;
    }

    /* Mobile navbar improvements */
    .navbar-modern .container-fluid {
        padding: 0.5rem 1rem;
    }

    .navbar-nav .nav-link {
        padding: 0.75rem 1rem;
        border-radius: 0;
    }

    .navbar-collapse {
        margin-top: 1rem;
        border-top: 1px solid var(--border-light);
        padding-top: 1rem;
    }

    .navbar-nav {
        width: 100%;
    }

    .navbar-nav .nav-item {
        width: 100%;
    }

    .navbar-nav .nav-link {
        width: 100%;
        text-align: left;
        border-bottom: 1px solid rgba(0,0,0,0.05);
    }

    /* Mobile brand adjustments */
    .brand-icon {
        width: 35px;
        height: 35px;
        margin-right: 0.5rem;
    }

    .brand-text .brand-main {
        font-size: 1.25rem;
    }

    .brand-text .brand-sub {
        font-size: 0.65rem;
    }
}
/* Mobile Small (up to 575px) */
@media (max-width: 576px) {
    .main-story-title {
        font-size: 1.25rem;
        line-height: 1.3;
    }

    .section-title {
        font-size: 1.25rem;
    }

    .category-title {
        font-size: 1.5rem;
    }

    .sub-news-item {
        flex-direction: column;
        gap: 0.5rem;
    }

    .sub-news-image {
        width: 100%;
        height: 120px;
    }

    .live-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.25rem;
    }

    .story-engagement {
        flex-direction: column;
        gap: 0.5rem;
    }

    /* Ultra-mobile optimizations */
    .container-fluid {
        padding-left: 0.75rem;
        padding-right: 0.75rem;
    }

    .navbar-modern .container-fluid {
        padding: 0.5rem 0.75rem;
    }

    .brand-text .brand-main {
        font-size: 1.1rem;
    }

    .brand-text .brand-sub {
        font-size: 0.6rem;
    }

    .main-story-card {
        margin-bottom: 1rem;
    }

    .story-content {
        padding: 0.75rem;
    }

    .main-story-excerpt {
        font-size: 0.9rem;
        line-height: 1.4;
    }

    /* Improve touch targets */
    .navbar-nav .nav-link {
        padding: 1rem;
        font-size: 1rem;
        min-height: 48px;
        display: flex;
        align-items: center;
    }

    .btn {
        min-height: 44px;
        padding: 0.75rem 1rem;
    }

    /* Better spacing for small screens */
    .row.g-4 {
        --bs-gutter-x: 1rem;
        --bs-gutter-y: 1rem;
    }
}

/* Extra small devices (landscape phones, less than 480px) */
@media (max-width: 480px) {
    .brand-text .brand-main {
        font-size: 1rem;
    }

    .brand-text .brand-sub {
        font-size: 0.55rem;
    }

    .main-story-title {
        font-size: 1.1rem;
    }

    .main-story-excerpt {
        font-size: 0.85rem;
    }

    .story-content {
        padding: 0.5rem;
    }
}

h5 { font-size: 1.25rem; font-weight: 600; }
h6 { font-size: 1.125rem; font-weight: 600; }

p {
    margin-bottom: var(--spacing-md);
    color: var(--text-secondary);
    line-height: 1.7;
}

a {
    color: var(--primary-color);
    text-decoration: none;
    transition: var(--transition-fast);
}

a:hover {
    color: var(--primary-dark);
    text-decoration: underline;
}

/* Improved Button Styles */
.btn {
    font-family: var(--font-primary);
    font-weight: 600;
    border-radius: var(--radius-md);
    padding: var(--spacing-sm) var(--spacing-lg);
    transition: var(--transition-normal);
    border: none;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-sm);
    text-decoration: none;
}

.btn-primary {
    background: var(--gradient-primary);
    color: white;
    box-shadow: var(--shadow-sm);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
    color: white;
    text-decoration: none;
}

.btn-modern {
    background: var(--gradient-primary);
    color: white;
    border: none;
    padding: var(--spacing-sm) var(--spacing-lg);
    border-radius: var(--radius-full);
    font-weight: 600;
    transition: var(--transition-normal);
    box-shadow: var(--shadow-sm);
}

.btn-modern:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
    color: white;
    text-decoration: none;
}

/* Enhanced Breaking News Banner */
.breaking-news-banner {
    background: var(--gradient-secondary);
    color: white;
    padding: 12px 0;
    font-size: 14px;
    font-weight: 500;
    animation: slideDown 0.5s ease-out;
    position: relative;
    overflow: hidden;
    border-bottom: 2px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.breaking-news-banner::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    animation: shimmer 3s infinite;
}

@keyframes shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
}

.breaking-news-banner .ticker {
    white-space: nowrap;
    overflow: hidden;
    position: relative;
    animation: ticker 30s linear infinite;
    padding: 0 20px;
}

@keyframes ticker {
    0% { transform: translateX(100%); }
    100% { transform: translateX(-100%); }
}

@keyframes slideDown {
    from {
        transform: translateY(-100%);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

/* Breaking News Badge Improvements */
.breaking-badge {
    background: var(--secondary-color);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: var(--radius-full);
    font-size: 0.875rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    animation: pulse 2s infinite;
    box-shadow: 0 2px 8px rgba(209, 52, 56, 0.3);
}

/* Responsive Breaking News */
@media (max-width: 768px) {
    .breaking-news-banner {
        padding: 10px 0;
        font-size: 13px;
    }

    .breaking-news-banner .ticker {
        padding: 0 15px;
        animation: ticker 25s linear infinite;
    }
}

@media (max-width: 576px) {
    .breaking-news-banner {
        padding: 8px 0;
        font-size: 12px;
    }

    .breaking-news-banner .ticker {
        padding: 0 10px;
        animation: ticker 20s linear infinite;
    }
}

@keyframes scroll-left {
    0% { transform: translate3d(100%, 0, 0); }
    100% { transform: translate3d(-100%, 0, 0); }
}

@keyframes slideDown {
    from { transform: translateY(-100%); }
    to { transform: translateY(0); }
}

/* Navigation */
.navbar-modern {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
    padding: 0;
    border-bottom: 2px solid rgba(30, 58, 138, 0.1);
    transition: all 0.3s ease;
}

.navbar-modern.scrolled {
    background: rgba(255, 255, 255, 0.98);
    box-shadow: 0 4px 30px rgba(0, 0, 0, 0.15);
    border-bottom: 2px solid rgba(30, 58, 138, 0.2);
}

/* Enhanced Navbar Container Alignment */
.navbar-modern .container-fluid {
    display: flex;
    align-items: center;
    justify-content: space-between;
    min-height: 70px;
    padding: 0.75rem 1.5rem;
    position: relative;
}

.navbar-modern .navbar-collapse {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    margin-left: 1rem;
}

.navbar-modern .navbar-nav {
    display: flex;
    align-items: center;
    margin: 0;
}

/* Enhanced Navbar Brand */
.navbar-brand {
    display: flex !important;
    align-items: center !important;
    text-decoration: none !important;
    transition: all 0.3s ease;
    padding: 0.5rem 0 !important;
    margin-right: 1rem !important;
    flex-shrink: 0;
    position: relative;
    z-index: 1000;
}

.navbar-brand:hover {
    transform: scale(1.02);
    filter: brightness(1.05);
    text-decoration: none !important;
}

.navbar-brand:focus {
    outline: none;
    text-decoration: none !important;
}

/* Enhanced Brand Icon Styling */
.brand-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 42px;
    height: 42px;
    background: linear-gradient(135deg, #0078d4 0%, #106ebe 50%, #d13438 100%);
    border-radius: 10px;
    margin-right: 0.75rem;
    flex-shrink: 0;
    box-shadow: 0 2px 8px rgba(0, 120, 212, 0.3);
    transition: all 0.3s ease;
}

.brand-icon i {
    font-size: 1.5rem;
    color: white;
    margin: 0 !important;
    line-height: 1;
}

/* Enhanced Brand Text Styling */
.brand-text {
    display: flex;
    flex-direction: column;
    line-height: 1;
    align-items: flex-start;
    justify-content: center;
    height: 42px;
}

.brand-main {
    font-size: 1.6rem;
    font-weight: 800;
    color: var(--primary-color);
    letter-spacing: -0.5px;
    margin: 0;
    line-height: 0.9;
    display: block;
}

.brand-sub {
    font-size: 0.75rem;
    font-weight: 600;
    color: var(--secondary-color);
    letter-spacing: 1.5px;
    margin: 0;
    line-height: 1;
    margin-top: 1px;
    display: block;
}

/* Enhanced Navigation Links */
.navbar-nav .nav-link {
    color: var(--text-primary);
    font-weight: 600;
    padding: 0.75rem 1.25rem;
    transition: all 0.3s ease;
    position: relative;
    text-decoration: none;
    border-radius: 8px;
    margin: 0 0.25rem;
    display: flex;
    align-items: center;
    white-space: nowrap;
}

.navbar-nav .nav-link:hover,
.navbar-nav .nav-link.active {
    color: var(--primary-color);
    text-decoration: none;
    background: rgba(0, 120, 212, 0.05);
    transform: translateY(-1px);
}

.navbar-nav .nav-link i {
    margin-right: 0.5rem;
    font-size: 0.9rem;
}

/* Enhanced underline effect */
.navbar-nav .nav-link::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 3px;
    background: linear-gradient(135deg, #0078d4 0%, #d13438 100%);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    transform: translateX(-50%);
    border-radius: 2px 2px 0 0;
    box-shadow: 0 2px 8px rgba(0, 120, 212, 0.3);
}

.navbar-nav .nav-link:hover::after {
    width: 80%;
}

.navbar-nav .nav-link.active::after {
    width: 90%;
    box-shadow: 0 4px 12px rgba(0, 120, 212, 0.4);
}

/* Enhanced Signup Button */
.navbar-nav .btn-modern {
    background: var(--gradient-primary);
    border: none;
    color: white !important;
    padding: 0.6rem 1.5rem;
    border-radius: 25px;
    font-weight: 600;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    margin-left: 0.5rem;
    box-shadow: 0 2px 8px rgba(0, 120, 212, 0.3);
    text-decoration: none !important;
    display: inline-flex;
    align-items: center;
    white-space: nowrap;
}

.navbar-nav .btn-modern:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 120, 212, 0.4);
    color: white !important;
    text-decoration: none !important;
}

.navbar-nav .btn-modern:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(0, 120, 212, 0.2);
}

/* Hero Section */
.hero-section {
    background: var(--bg-light);
    padding: 2rem 0;
}

.hero-card {
    background: var(--white);
    border-radius: 12px;
    overflow: hidden;
    box-shadow: var(--shadow-lg);
    transition: transform 0.3s ease;
}

.hero-card:hover {
    transform: translateY(-5px);
}

.hero-image {
    width: 100%;
    height: 300px;
    object-fit: cover;
}

.hero-content {
    padding: 1.5rem;
}

.hero-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-dark);
    margin-bottom: 0.5rem;
    line-height: 1.3;
}

.hero-excerpt {
    color: var(--text-light);
    margin-bottom: 1rem;
}

.hero-meta {
    display: flex;
    align-items: center;
    gap: 1rem;
    font-size: 0.875rem;
    color: var(--text-light);
}

/* Enhanced News Cards - NBC Style */
.news-card {
    background: var(--bg-primary);
    border-radius: var(--radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
    transition: var(--transition-normal);
    height: 100%;
    border: 1px solid var(--border-light);
}

.news-card:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-3px);
    border-color: var(--primary-color);
}

.news-image {
    width: 100%;
    height: 220px;
    object-fit: cover;
    transition: var(--transition-normal);
}

.news-card:hover .news-image {
    transform: scale(1.05);
}

.news-content {
    padding: 1.5rem;
}

.news-title {
    font-size: 1.125rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 0.75rem;
    line-height: 1.3;
}

.news-title a {
    color: inherit;
    text-decoration: none;
    transition: var(--transition-fast);
}

.news-title a:hover {
    color: var(--primary-color);
}

.news-excerpt {
    color: var(--text-secondary);
    font-size: 0.95rem;
    margin-bottom: 1rem;
    line-height: 1.6;
}

.news-meta {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 0.875rem;
    color: var(--text-muted);
    flex-wrap: wrap;
    gap: 0.5rem;
}

/* Enhanced Category Badges */
.category-badge {
    display: inline-block;
    padding: 0.25rem 0.75rem;
    background: var(--primary-color);
    color: white;
    font-size: 0.75rem;
    font-weight: 600;
    border-radius: var(--radius-full);
    text-decoration: none;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: var(--transition-fast);
}

.category-badge:hover {
    background: var(--primary-dark);
    color: white;
    text-decoration: none;
    color: white;
}

/* Priority Badges */
.priority-breaking {
    background: var(--secondary-color);
    color: white;
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    border-radius: 4px;
    font-weight: 600;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

.priority-high {
    background: var(--accent-color);
    color: white;
}

/* Sidebar */
.sidebar-widget {
    background: var(--white);
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    box-shadow: var(--shadow-sm);
}

.widget-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid var(--primary-color);
}

/* Advertisement Slots */
.ad-banner {
    background: var(--bg-light);
    border: 2px dashed var(--border-color);
    border-radius: 8px;
    padding: 2rem;
    text-align: center;
    margin: 2rem 0;
    transition: all 0.3s ease;
}

.ad-banner:hover {
    border-color: var(--primary-color);
    background: rgba(30, 58, 138, 0.05);
}

.ad-content {
    max-width: 100%;
    height: auto;
    border-radius: 8px;
}

/* Footer */
.footer-modern {
    background: var(--text-dark);
    color: white;
    padding: 3rem 0 1rem;
    margin-top: 4rem;
}

.footer-section h5 {
    color: white;
    margin-bottom: 1rem;
    font-weight: 600;
}

.footer-section a {
    color: #d1d5db;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section a:hover {
    color: white;
}

.footer-bottom {
    border-top: 1px solid #374151;
    padding-top: 1rem;
    margin-top: 2rem;
    text-align: center;
    color: #9ca3af;
}

/* Icon Alignment Fixes for All Titles */
h1, h2, h3, h4, h5, h6,
.main-story-title, .section-title, .category-title,
.secondary-story-title, .sub-news-title, .live-title,
.latest-news-title, .category-story-title, .sidebar-title,
.related-story-title, .tech-story-title, .trending-tech-title,
.hero-title, .news-title, .widget-title {
    display: flex;
    align-items: center;
    line-height: 1.3;
}

h1 i, h2 i, h3 i, h4 i, h5 i, h6 i,
.main-story-title i, .section-title i, .category-title i,
.secondary-story-title i, .sub-news-title i, .live-title i,
.latest-news-title i, .category-story-title i, .sidebar-title i,
.related-story-title i, .tech-story-title i, .trending-tech-title i,
.hero-title i, .news-title i, .widget-title i {
    margin-right: 0.5rem;
    color: var(--primary-color);
    vertical-align: middle;
    flex-shrink: 0;
    font-size: 0.9em;
}

/* Story meta icons alignment */
.story-meta i, .secondary-story-meta i, .sub-news-meta i,
.latest-news-meta i, .engagement-item i {
    margin-right: 0.25rem;
    vertical-align: middle;
    color: var(--text-muted);
    font-size: 0.875em;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-title {
        font-size: 1.25rem;
    }

    .navbar-nav .nav-link {
        padding: 0.75rem 1rem;
    }

    /* Responsive Navbar Brand */
    .brand-icon {
        width: 35px;
        height: 35px;
        margin-right: 0.5rem;
    }

    .brand-icon i {
        font-size: 1.25rem;
    }

    .brand-main {
        font-size: 1.25rem;
    }

    .brand-sub {
        font-size: 0.7rem;
    }
    }
    
    .hero-image {
        height: 200px;
    }
    
    .news-image {
        height: 150px;
    }
}

/* Loading Animation */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(30, 58, 138, 0.3);
    border-radius: 50%;
    border-top-color: var(--primary-color);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Utility Classes */
.text-gradient {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.btn-modern {
    background: var(--gradient-primary);
    border: none;
    color: white;
    padding: 0.75rem 2rem;
    border-radius: 25px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-modern:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
    color: white;
}

/* ===== RESPONSIVE UTILITIES ===== */

/* Responsive Text Utilities */
.text-responsive {
    font-size: clamp(0.875rem, 2.5vw, 1.125rem);
}

.title-responsive {
    font-size: clamp(1.25rem, 4vw, 2.5rem);
    line-height: 1.2;
}

.subtitle-responsive {
    font-size: clamp(1rem, 3vw, 1.5rem);
    line-height: 1.3;
}

/* Responsive Spacing */
.spacing-responsive {
    padding: clamp(1rem, 3vw, 2rem);
}

.margin-responsive {
    margin: clamp(0.5rem, 2vw, 1.5rem);
}

/* Responsive Grid Utilities */
.grid-responsive {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: clamp(1rem, 3vw, 2rem);
}

.grid-responsive-2 {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: clamp(0.75rem, 2vw, 1.5rem);
}

/* Responsive Flexbox Utilities */
.flex-responsive {
    display: flex;
    flex-wrap: wrap;
    gap: clamp(0.5rem, 2vw, 1rem);
}

.flex-responsive-center {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    align-items: center;
    gap: clamp(0.5rem, 2vw, 1rem);
}

/* Responsive Image Utilities */
.img-responsive {
    width: 100%;
    height: auto;
    object-fit: cover;
    border-radius: var(--radius-md);
}

.img-responsive-square {
    width: 100%;
    aspect-ratio: 1;
    object-fit: cover;
    border-radius: var(--radius-md);
}

.img-responsive-wide {
    width: 100%;
    aspect-ratio: 16/9;
    object-fit: cover;
    border-radius: var(--radius-md);
}

/* Responsive Container Utilities */
.container-responsive {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 clamp(1rem, 3vw, 2rem);
}

.container-responsive-wide {
    width: 100%;
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 clamp(1rem, 3vw, 2rem);
}

/* Responsive Visibility Utilities */
.hide-mobile {
    display: block;
}

.show-mobile {
    display: none;
}

.hide-tablet {
    display: block;
}

.show-tablet {
    display: none;
}

@media (max-width: 768px) {
    .hide-mobile {
        display: none !important;
    }

    .show-mobile {
        display: block !important;
    }
}

@media (max-width: 992px) and (min-width: 769px) {
    .hide-tablet {
        display: none !important;
    }

    .show-tablet {
        display: block !important;
    }
}

/* Responsive Typography Scale */
@media (max-width: 576px) {
    h1 { font-size: 1.75rem; }
    h2 { font-size: 1.5rem; }
    h3 { font-size: 1.25rem; }
    h4 { font-size: 1.125rem; }
    h5 { font-size: 1rem; }
    h6 { font-size: 0.875rem; }
}

/* Responsive Card Improvements */
@media (max-width: 768px) {
    .card {
        margin-bottom: 1rem;
    }

    .card-body {
        padding: 1rem;
    }

    .card-title {
        font-size: 1.1rem;
        line-height: 1.3;
    }
}

/* Responsive Navigation Improvements */
@media (max-width: 991px) {
    .navbar-toggler {
        border: none;
        padding: 0.25rem 0.5rem;
        font-size: 1.25rem;
    }

    .navbar-toggler:focus {
        box-shadow: none;
    }

    .navbar-collapse {
        background: rgba(255, 255, 255, 0.98);
        border-radius: var(--radius-md);
        margin-top: 0.5rem;
        padding: 1rem;
        box-shadow: var(--shadow-md);
    }
}
