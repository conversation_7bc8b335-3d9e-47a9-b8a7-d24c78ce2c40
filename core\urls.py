"""core URL Configuration

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/3.1/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static

from rest_framework import permissions
from drf_yasg.views import get_schema_view
from drf_yasg import openapi


schema_view = get_schema_view(
    openapi.Info(
        title="Snippets API",
        default_version='v1',
        description="Test description",
        terms_of_service="https://www.google.com/policies/terms/",
        contact=openapi.Contact(email="<EMAIL>"),
        license=openapi.License(name="BSD License"),
    ),
    public=True,
    permission_classes=(permissions.AllowAny,),
)


urlpatterns = [
    path('', include('mainsite.urls', namespace='mainsite')),
    path('comment/', include('comment.urls')),
    path('account/', include('account.urls')),
    path('subscribe/', include('subscription.urls')),
    path('admin/', admin.site.urls),
    path('custom-admin/', include('custom_admin.urls', namespace='custom_admin')),
    path('api/', include(
        [

            path('', include('mainsite.api.urls')),
            path('', include('comment.api.urls')),
            path('', include('news.api.urls')),
            path('', include('subscription.api.urls')),
            path('account/', include('account.api.urls')),
            path('newsdata/', include('news.api_urls', namespace='news_api')),
            path('swagger/', schema_view.with_ui('swagger', cache_timeout=0)),
        ]
    )
    ),
]
urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)
urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
