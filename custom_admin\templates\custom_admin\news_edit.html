{% extends 'custom_admin/base.html' %}
{% load static %}

{% block title %}Edit Article{% endblock %}
{% block page_title %}Edit Article{% endblock %}

{% block extra_css %}
<link href="https://cdn.quilljs.com/1.3.6/quill.snow.css" rel="stylesheet">
<style>
.editor-container {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.form-section {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    border: 1px solid #e9ecef;
}

.form-section h5 {
    color: #1e3a8a;
    margin-bottom: 1.5rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.form-control, .form-select {
    border-radius: 8px;
    border: 2px solid #e9ecef;
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
}

.form-control:focus, .form-select:focus {
    border-color: #0078d4;
    box-shadow: 0 0 0 0.2rem rgba(0, 120, 212, 0.25);
}

.btn-save {
    background: linear-gradient(135deg, #0078d4 0%, #106ebe 100%);
    border: none;
    padding: 0.75rem 2rem;
    border-radius: 8px;
    color: white;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-save:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 120, 212, 0.3);
    color: white;
}

.btn-preview {
    background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
    border: none;
    padding: 0.75rem 2rem;
    border-radius: 8px;
    color: white;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-preview:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(108, 117, 125, 0.3);
    color: white;
}

.status-badge {
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 600;
}

.status-published { background: #d4edda; color: #155724; }
.status-draft { background: #f8d7da; color: #721c24; }
.status-featured { background: #fff3cd; color: #856404; }

#editor {
    height: 400px;
}

.image-preview {
    max-width: 200px;
    max-height: 150px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.tag-input {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    padding: 0.5rem;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    min-height: 45px;
}

.tag-item {
    background: #0078d4;
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.875rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.tag-remove {
    cursor: pointer;
    font-weight: bold;
}
</style>
{% endblock %}

{% block content %}
<form method="post" enctype="multipart/form-data" id="newsForm">
    {% csrf_token %}
    
    <!-- Article Status -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div class="d-flex gap-2">
                    {% if article.is_published %}
                        <span class="status-badge status-published">Published</span>
                    {% else %}
                        <span class="status-badge status-draft">Draft</span>
                    {% endif %}
                    
                    {% if article.is_featured %}
                        <span class="status-badge status-featured">Featured</span>
                    {% endif %}
                </div>
                
                <div class="d-flex gap-2">
                    <button type="button" class="btn btn-preview" onclick="previewArticle()">
                        <i class="fas fa-eye"></i> Preview
                    </button>
                    <button type="submit" class="btn btn-save">
                        <i class="fas fa-save"></i> Save Article
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Main Content -->
        <div class="col-lg-8">
            <!-- Basic Information -->
            <div class="form-section">
                <h5><i class="fas fa-edit"></i> Article Details</h5>
                
                <div class="mb-3">
                    <label for="title" class="form-label">Title *</label>
                    <input type="text" class="form-control" id="title" name="title" 
                           value="{{ article.title|default:'' }}" required>
                </div>
                
                <div class="mb-3">
                    <label for="slug" class="form-label">URL Slug</label>
                    <input type="text" class="form-control" id="slug" name="slug" 
                           value="{{ article.slug|default:'' }}">
                    <small class="text-muted">Leave blank to auto-generate from title</small>
                </div>
                
                <div class="mb-3">
                    <label for="description" class="form-label">Description *</label>
                    <textarea class="form-control" id="description" name="description" 
                              rows="3" required>{{ article.description|default:'' }}</textarea>
                    <small class="text-muted">Brief summary that appears in previews</small>
                </div>
            </div>

            <!-- Content Editor -->
            <div class="form-section">
                <h5><i class="fas fa-file-alt"></i> Article Content</h5>
                <div class="editor-container">
                    <div id="editor">{{ article.content|default:'' }}</div>
                </div>
                <textarea name="content" id="content-hidden" style="display: none;"></textarea>
            </div>

            <!-- Tags -->
            <div class="form-section">
                <h5><i class="fas fa-tags"></i> Tags</h5>
                <div class="tag-input" id="tagInput">
                    {% for tag in article.tags.all %}
                        <span class="tag-item">
                            {{ tag.name }}
                            <span class="tag-remove" onclick="removeTag(this)">×</span>
                        </span>
                    {% endfor %}
                </div>
                <input type="text" class="form-control mt-2" id="newTag" 
                       placeholder="Type a tag and press Enter">
                <input type="hidden" name="tags" id="tagsHidden">
            </div>
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Publishing Options -->
            <div class="form-section">
                <h5><i class="fas fa-cog"></i> Publishing</h5>
                
                <div class="mb-3">
                    <label for="category" class="form-label">Category *</label>
                    <select class="form-select" id="category" name="category" required>
                        <option value="">Select Category</option>
                        {% for category in categories %}
                            <option value="{{ category.id }}" 
                                    {% if article.category.id == category.id %}selected{% endif %}>
                                {{ category.name }}
                            </option>
                        {% endfor %}
                    </select>
                </div>
                
                <div class="mb-3">
                    <label for="author" class="form-label">Author</label>
                    <select class="form-select" id="author" name="author">
                        {% for author in authors %}
                            <option value="{{ author.id }}" 
                                    {% if article.author.id == author.id %}selected{% endif %}>
                                {{ author.user.get_full_name|default:author.user.username }}
                            </option>
                        {% endfor %}
                    </select>
                </div>
                
                <div class="mb-3">
                    <label for="publish_date" class="form-label">Publish Date</label>
                    <input type="datetime-local" class="form-control" id="publish_date" 
                           name="publish_date" value="{{ article.publish_date|date:'Y-m-d\TH:i' }}">
                </div>
            </div>

            <!-- Article Options -->
            <div class="form-section">
                <h5><i class="fas fa-star"></i> Options</h5>
                
                <div class="form-check mb-2">
                    <input class="form-check-input" type="checkbox" id="is_published" 
                           name="is_published" {% if article.is_published %}checked{% endif %}>
                    <label class="form-check-label" for="is_published">
                        Published
                    </label>
                </div>
                
                <div class="form-check mb-2">
                    <input class="form-check-input" type="checkbox" id="is_featured" 
                           name="is_featured" {% if article.is_featured %}checked{% endif %}>
                    <label class="form-check-label" for="is_featured">
                        Featured Article
                    </label>
                </div>
                
                <div class="form-check mb-2">
                    <input class="form-check-input" type="checkbox" id="is_breaking" 
                           name="is_breaking" {% if article.is_breaking %}checked{% endif %}>
                    <label class="form-check-label" for="is_breaking">
                        Breaking News
                    </label>
                </div>
                
                <div class="form-check mb-3">
                    <input class="form-check-input" type="checkbox" id="is_trending" 
                           name="is_trending" {% if article.is_trending %}checked{% endif %}>
                    <label class="form-check-label" for="is_trending">
                        Trending
                    </label>
                </div>
                
                <div class="mb-3">
                    <label for="priority" class="form-label">Priority</label>
                    <select class="form-select" id="priority" name="priority">
                        <option value="low" {% if article.priority == 'low' %}selected{% endif %}>Low</option>
                        <option value="normal" {% if article.priority == 'normal' %}selected{% endif %}>Normal</option>
                        <option value="high" {% if article.priority == 'high' %}selected{% endif %}>High</option>
                        <option value="breaking" {% if article.priority == 'breaking' %}selected{% endif %}>Breaking</option>
                    </select>
                </div>
            </div>

            <!-- Featured Image -->
            <div class="form-section">
                <h5><i class="fas fa-image"></i> Featured Image</h5>
                
                {% if article.image %}
                    <div class="mb-3">
                        <img src="{{ article.image.url }}" alt="Current image" class="image-preview">
                    </div>
                {% endif %}
                
                <div class="mb-3">
                    <input type="file" class="form-control" id="image" name="image" accept="image/*">
                </div>
                
                <div class="mb-3">
                    <label for="thumbnail_url" class="form-label">Or Image URL</label>
                    <input type="url" class="form-control" id="thumbnail_url" 
                           name="thumbnail_url" value="{{ article.thumbnail_url|default:'' }}">
                </div>
            </div>
        </div>
    </div>
</form>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.quilljs.com/1.3.6/quill.min.js"></script>
<script>
// Initialize Quill editor
var quill = new Quill('#editor', {
    theme: 'snow',
    modules: {
        toolbar: [
            [{ 'header': [1, 2, 3, false] }],
            ['bold', 'italic', 'underline', 'strike'],
            [{ 'color': [] }, { 'background': [] }],
            [{ 'list': 'ordered'}, { 'list': 'bullet' }],
            [{ 'align': [] }],
            ['link', 'image', 'video'],
            ['blockquote', 'code-block'],
            ['clean']
        ]
    }
});

// Auto-generate slug from title
document.getElementById('title').addEventListener('input', function() {
    const title = this.value;
    const slug = title.toLowerCase()
        .replace(/[^a-z0-9\s-]/g, '')
        .replace(/\s+/g, '-')
        .replace(/-+/g, '-')
        .trim('-');
    document.getElementById('slug').value = slug;
});

// Tag management
let tags = [];
{% for tag in article.tags.all %}
    tags.push('{{ tag.name }}');
{% endfor %}

document.getElementById('newTag').addEventListener('keypress', function(e) {
    if (e.key === 'Enter') {
        e.preventDefault();
        const tagValue = this.value.trim();
        if (tagValue && !tags.includes(tagValue)) {
            addTag(tagValue);
            this.value = '';
        }
    }
});

function addTag(tagName) {
    tags.push(tagName);
    updateTagDisplay();
    updateTagsHidden();
}

function removeTag(element) {
    const tagName = element.parentElement.textContent.replace('×', '').trim();
    tags = tags.filter(tag => tag !== tagName);
    updateTagDisplay();
    updateTagsHidden();
}

function updateTagDisplay() {
    const tagInput = document.getElementById('tagInput');
    tagInput.innerHTML = '';

    tags.forEach(tag => {
        const tagElement = document.createElement('span');
        tagElement.className = 'tag-item';
        tagElement.innerHTML = `${tag} <span class="tag-remove" onclick="removeTag(this)">×</span>`;
        tagInput.appendChild(tagElement);
    });
}

function updateTagsHidden() {
    document.getElementById('tagsHidden').value = tags.join(',');
}

// Form submission
document.getElementById('newsForm').addEventListener('submit', function(e) {
    // Get content from Quill editor
    const content = quill.root.innerHTML;
    document.getElementById('content-hidden').value = content;

    // Update tags
    updateTagsHidden();
});

// Preview function
function previewArticle() {
    const title = document.getElementById('title').value;
    const content = quill.root.innerHTML;

    // Open preview in new window
    const previewWindow = window.open('', '_blank');
    previewWindow.document.write(`
        <html>
            <head>
                <title>Preview: ${title}</title>
                <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
                <style>
                    body { padding: 2rem; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; }
                    .preview-header { border-bottom: 2px solid #0078d4; padding-bottom: 1rem; margin-bottom: 2rem; }
                    .preview-content { line-height: 1.8; }
                </style>
            </head>
            <body>
                <div class="container">
                    <div class="preview-header">
                        <h1>${title}</h1>
                        <p class="text-muted">Preview Mode</p>
                    </div>
                    <div class="preview-content">
                        ${content}
                    </div>
                </div>
            </body>
        </html>
    `);
}

// Initialize tags display
updateTagDisplay();
updateTagsHidden();
</script>
{% endblock %}
