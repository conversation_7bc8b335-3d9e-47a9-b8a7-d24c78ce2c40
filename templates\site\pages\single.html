{% extends 'site/base.html' %}
{% load static %}
{% block content %}
<section id="entity_section" class="entity_section">
    <div class="container">
        <div class="row">
            <div class="col-md-8">
                <div class="entity_wrapper">
                    <div class="entity_title">
                        <h1><a href="#">{{ news.title }}</a></h1>
                    </div>
                    <!-- entity_title -->

                    <div class="entity_meta"><a href="#" target="_self">{{ news.timestamp|date:"j M Y" }}</a>, by: <a
                            href="#" target="_self">{{ news.author|title }}</a>
                    </div>
                    <!-- entity_meta -->

                    <div class="entity_rating">
                        <i class="fa fa-star"></i>
                        <i class="fa fa-star"></i>
                        <i class="fa fa-star"></i>
                        <i class="fa fa-star"></i>
                        <i class="fa fa-star-half-full"></i>
                    </div>
                    <!-- entity_rating -->

                    <div class="entity_social">
                        <a href="#" class="icons-sm sh-ic">
                            <i class="fa fa-share-alt"></i>
                            <b>424</b> <span class="share_ic">Shares</span>
                        </a>
                        <a href="#" class="icons-sm fb-ic"><i class="fa fa-facebook"></i></a>
                        <!--Twitter-->
                        <a href="#" class="icons-sm tw-ic"><i class="fa fa-twitter"></i></a>
                        <!--Google +-->
                        <a href="#" class="icons-sm inst-ic"><i class="fa fa-google-plus"> </i></a>
                        <!--Linkedin-->
                        <a href="#" class="icons-sm tmb-ic"><i class="fa fa-ge"> </i></a>
                        <!--Pinterest-->
                        <a href="#" class="icons-sm rss-ic"><i class="fa fa-rss"> </i></a>
                    </div>
                    <!-- entity_social -->

                    <div class="entity_thumb">
                        <img class="img-responsive" style="width: 775px; max-height:450px;"
                            src="{% if news.thumbnail_url %}{{ news.thumbnail_url }}{% elif news.thumbnail %}{{ news.thumbnail.url }}{% else %}{% static 'assets/img/feature-top.jpg' %}{% endif %}"
                            alt="{{ news.title }}"
                            onerror="this.src='{% static 'assets/img/feature-top.jpg' %}'"
                            loading="lazy">
                    </div>
                    <!-- entity_thumb -->

                    <div class="entity_content">
                        <p>
                            {{ news.description }}
                        </p>
                    </div>
                    <!-- entity_content -->

                    <div class="entity_footer">
                        <div class="entity_tag">

                            {% for tag in news.tags.all %}
                            <span class="blank"><a href="{% url 'newspaper:tag' tag %}">{{ tag }}</a></span>
                            {% endfor %}

                        </div>
                        <!-- entity_tag -->

                        <div class="entity_social">
                            <span><i class="fa fa-share-alt"></i>424 <a href="#">Shares</a> </span>
                            <span><i class="fa fa-comments-o"></i>{{ news.get_comment_count }} <a href="#">Comments</a> </span>
                        </div>
                        <!-- entity_social -->

                    </div>
                    <!-- entity_footer -->

                </div>
                <!-- entity_wrapper -->

                <div class="related_news">
                    <div class="entity_inner__title header_purple">
                        <h2><a href="#">Related News</a></h2>
                    </div>
                    <!-- entity_title -->

                    <div class="row">

                        <div class="col-md-12">
                            {% for post in related_posts %}
                            <div class="col-md-6">
                                <div class="media">
                                    <div class="media-left">
                                        <a href="{{ post.get_absolute_url }}"><img class="media-object" style="width: 122px; height: 122px;"
                                                src="{% if post.thumbnail_url %}{{ post.thumbnail_url }}{% elif post.thumbnail %}{{ post.thumbnail.url }}{% else %}{% static 'assets/img/category_img1.jpg' %}{% endif %}"
                                                alt="{{ post.title }}"
                                                onerror="this.src='{% static 'assets/img/category_img1.jpg' %}'"
                                                loading="lazy"></a>
                                    </div>
                                    <div class="media-body">
                                        <span class="tag purple"><a href="{{ post.category.get_absolute_url }}"
                                                target="_self">{{ post.category }}</a></span>

                                        <h3 class="media-heading"><a href="{{ post.get_absolute_url }}"
                                                target="_self">{{ post.title }}</a></h3>
                                        <span class="media-date">{{ post.timestamp|date:"j M Y" }}, by: <a
                                                href="#">{{ post.author|title }}</a></span>

                                        <div class="media_social">
                                            <span><a href="#"><i class="fa fa-share-alt"></i>424</a> Shares</span>
                                            <span><a href="#"><i class="fa fa-comments-o"></i>4</a> Comments</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                </div>
                <!-- Related news -->

                <div class="widget_advertisement">
                    <img class="img-responsive" src="{% static 'assets/img/category_advertisement.jpg' %}"
                        alt="feature-top">
                </div>
                <!--Advertisement-->

                <div class="readers_comment">
                    <div class="entity_inner__title header_purple">
                        <h2>Readers Comment</h2>
                    </div>
                    <!-- entity_title -->
                    {% for comment in comments %}
                    <div class="media">
                        <div class="media-left">
                            <a href="#">
                                <img alt="64x64" class="media-object"
                                    data-src="{% static 'assets/img/reader_img1.jpg' %}"
                                    src="{% static 'assets/img/reader_img1.jpg' %}" data-holder-rendered="true">
                            </a>
                        </div>
                        <div class="media-body">
                            <h2 class="media-heading"><a href="#">{{ comment.user.username|capfirst }}</a></h2>
                            {{ comment.comment }}
                            <div class="entity_vote">
                                <a href="#"><i class="fa fa-thumbs-o-up" aria-hidden="true"></i></a>
                                <a href="#"><i class="fa fa-thumbs-o-down" aria-hidden="true"></i></a>
                                <button class="btn  btn-xs reply_ic">Reply</button>
                            </div>
                            {% for reply in comment.replies.all %}
                            <div class="media">
                                <div class="media-left">
                                    <a href="#">
                                        <img alt="64x64" class="media-object"
                                            data-src="{% static 'assets/img/reader_img2.jpg' %}"
                                            src="{% static 'assets/img/reader_img2.jpg' %}" data-holder-rendered="true">
                                    </a>
                                </div>
                                <div class="media-body">
                                    <h2 class="media-heading"><a href="#">{{ comment.user.username|capfirst }}</a></h2>
                                    {{ reply.comment }}

                                    <div class="entity_vote">
                                        <a href="#"><i class="fa fa-thumbs-o-up" aria-hidden="true"></i></a>
                                        <a href="#"><i class="fa fa-thumbs-o-down" aria-hidden="true"></i></a>
                                        <span class="reply_ic">Reply </span>
                                    </div>
                                </div>
                            </div>

                            {% endfor %}

                            {% if user.is_authenticated %} 
                            <div class="entity_comments toggle-rp" style="display: none;">
                                <div class="entity_inner__title header_black">
                                    <h2>Add a Reply</h2>
                                </div>
                                <!--Entity Title -->

                                <div class="entity_comment_from">
                                    <form method="POST" action="">
                                        {% csrf_token %}
                                        <input type="hidden" name="reply" id="id_reply" value="{{ comment.id }}">
                                        <input type="hidden" name="post" id="id_post" value="{{ news.id }}">
                                        <div class="form-group comment">
                                            <textarea class="form-control" name="comment" id="id_comment"
                                                placeholder="Comment"></textarea>
                                        </div>
                                        <button type="submit" class="btn btn-submit red">Submit</button>

                                    </form>
                                </div>
                                <!--Entity Comments From -->

                            </div>
                            {% endif %} 
                        </div>
                    </div>
                    {% endfor %}
                    <!-- media end -->
                </div>
                <!--Readers Comment-->

                <div class="widget_advertisement">
                    <img class="img-responsive" src="{% static 'assets/img/category_advertisement.jpg' %}"
                        alt="feature-top">
                </div>
                <div class="entity_comments">
                {% if user.is_authenticated %} 
                <!--Advertisement-->
                    <div class="entity_inner__title header_black">
                        <h2>Add a Comment</h2>
                    </div>
                    <!--Entity Title -->

                    <div class="entity_comment_from">
                        <form method="POST" action="">
                            {% csrf_token %}
                            <input type="hidden" name="post" id="id_post" value="{{ news.id }}">
                            <div class="form-group comment">
                                <textarea class="form-control" name="comment" id="id_comment"
                                    placeholder="Comment"></textarea>
                            </div>
                            <button type="submit" class="btn btn-submit red">Submit</button>
                        </form>
                    </div>
                    <!--Entity Comments From -->
                {% else %} 
                    <div class="entity_inner__title header_black">
                        <h2>Login For Comment</h2>
                    </div>

                    <div class="entity_comment_from">
                        
                        <button type="submit" class="btn btn-submit red">Login</button>
                        
                    </div>


                <!--Entity Comments -->
                {% endif %} 
                </div>
            </div>
            <!--Left Section-->
            <!--sidebar-->
            {% include 'site/includes/_sidebar.html' %}
        </div>
        <!-- row -->

    </div>
    <!-- container -->

</section>
<!-- Entity Section Wrapper -->



{% endblock %}

{% block script %}

<script>

    $('.reply_ic').click(function () {
        console.log($(this).parent().parent().parent().find('.toggle-rp').fadeToggle());
        $(this).find('.toggle-rp').fadeToggle();
    });

</script>

<style>
    .btn.btn-xs.reply_ic {
        padding: 0px 8px;
    }
</style>
{% endblock %}
