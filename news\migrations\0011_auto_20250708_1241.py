# Generated by Django 3.1.4 on 2025-07-08 06:56

from django.db import migrations, models
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        ('news', '0010_auto_20211024_1535'),
    ]

    operations = [
        migrations.CreateModel(
            name='Advertisement',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200)),
                ('ad_type', models.CharField(choices=[('banner', 'Banner'), ('sidebar', 'Sidebar'), ('popup', 'Popup'), ('inline', 'Inline Content'), ('header', 'Header'), ('footer', 'Footer')], max_length=20)),
                ('ad_size', models.CharField(choices=[('728x90', 'Leaderboard (728x90)'), ('300x250', 'Medium Rectangle (300x250)'), ('320x50', 'Mobile Banner (320x50)'), ('160x600', 'Wide Skyscraper (160x600)'), ('300x600', 'Half Page (300x600)'), ('970x250', 'Billboard (970x250)')], max_length=20)),
                ('image', models.ImageField(blank=True, null=True, upload_to='ads/%Y-%m-%d/')),
                ('html_content', models.TextField(blank=True, help_text='Custom HTML/JavaScript code')),
                ('url', models.URLField(blank=True, help_text='Click destination URL')),
                ('is_active', models.BooleanField(default=True)),
                ('start_date', models.DateTimeField(default=django.utils.timezone.now)),
                ('end_date', models.DateTimeField(blank=True, null=True)),
                ('clicks', models.PositiveIntegerField(default=0)),
                ('impressions', models.PositiveIntegerField(default=0)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'verbose_name': 'Advertisement',
                'verbose_name_plural': 'Advertisements',
                'db_table': 'advertisements',
            },
        ),
        migrations.CreateModel(
            name='BreakingNews',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200)),
                ('content', models.TextField()),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('expires_at', models.DateTimeField(blank=True, null=True)),
            ],
            options={
                'verbose_name': 'Breaking News',
                'verbose_name_plural': 'Breaking News',
                'db_table': 'breaking_news',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='Newsletter',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('email', models.EmailField(max_length=254, unique=True)),
                ('is_active', models.BooleanField(default=True)),
                ('subscribed_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'verbose_name': 'Newsletter Subscription',
                'verbose_name_plural': 'Newsletter Subscriptions',
                'db_table': 'newsletter_subscriptions',
            },
        ),
        migrations.AddField(
            model_name='news',
            name='content',
            field=models.TextField(blank=True, default='', help_text='Full article content'),
        ),
        migrations.AddField(
            model_name='news',
            name='is_breaking',
            field=models.BooleanField(default=False, help_text='Breaking news alert'),
        ),
        migrations.AddField(
            model_name='news',
            name='is_featured',
            field=models.BooleanField(default=False, help_text='Show on homepage featured section'),
        ),
        migrations.AddField(
            model_name='news',
            name='is_trending',
            field=models.BooleanField(default=False, help_text='Mark as trending news'),
        ),
        migrations.AddField(
            model_name='news',
            name='priority',
            field=models.CharField(choices=[('low', 'Low'), ('normal', 'Normal'), ('high', 'High'), ('breaking', 'Breaking News')], default='normal', max_length=10),
        ),
        migrations.AddField(
            model_name='news',
            name='publish_date',
            field=models.DateTimeField(default=django.utils.timezone.now),
        ),
        migrations.AddField(
            model_name='news',
            name='updated_at',
            field=models.DateTimeField(auto_now=True),
        ),
        migrations.AddField(
            model_name='news',
            name='views_count',
            field=models.PositiveIntegerField(default=0),
        ),
    ]
